function jy(i,r){for(var s=0;s<r.length;s++){const c=r[s];if(typeof c!="string"&&!Array.isArray(c)){for(const d in c)if(d!=="default"&&!(d in i)){const h=Object.getOwnPropertyDescriptor(c,d);h&&Object.defineProperty(i,d,h.get?h:{enumerable:!0,get:()=>c[d]})}}}return Object.freeze(Object.defineProperty(i,Symbol.toStringTag,{value:"Module"}))}(function(){const r=document.createElement("link").relList;if(r&&r.supports&&r.supports("modulepreload"))return;for(const d of document.querySelectorAll('link[rel="modulepreload"]'))c(d);new MutationObserver(d=>{for(const h of d)if(h.type==="childList")for(const v of h.addedNodes)v.tagName==="LINK"&&v.rel==="modulepreload"&&c(v)}).observe(document,{childList:!0,subtree:!0});function s(d){const h={};return d.integrity&&(h.integrity=d.integrity),d.referrerPolicy&&(h.referrerPolicy=d.referrerPolicy),d.crossOrigin==="use-credentials"?h.credentials="include":d.crossOrigin==="anonymous"?h.credentials="omit":h.credentials="same-origin",h}function c(d){if(d.ep)return;d.ep=!0;const h=s(d);fetch(d.href,h)}})();function hh(i){return i&&i.__esModule&&Object.prototype.hasOwnProperty.call(i,"default")?i.default:i}var ls={exports:{}},ni={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zm;function _y(){if(zm)return ni;zm=1;var i=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function s(c,d,h){var v=null;if(h!==void 0&&(v=""+h),d.key!==void 0&&(v=""+d.key),"key"in d){h={};for(var g in d)g!=="key"&&(h[g]=d[g])}else h=d;return d=h.ref,{$$typeof:i,type:c,key:v,ref:d!==void 0?d:null,props:h}}return ni.Fragment=r,ni.jsx=s,ni.jsxs=s,ni}var Um;function My(){return Um||(Um=1,ls.exports=_y()),ls.exports}var f=My(),as={exports:{}},oe={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Hm;function Ry(){if(Hm)return oe;Hm=1;var i=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),s=Symbol.for("react.fragment"),c=Symbol.for("react.strict_mode"),d=Symbol.for("react.profiler"),h=Symbol.for("react.consumer"),v=Symbol.for("react.context"),g=Symbol.for("react.forward_ref"),x=Symbol.for("react.suspense"),y=Symbol.for("react.memo"),E=Symbol.for("react.lazy"),T=Symbol.iterator;function R(S){return S===null||typeof S!="object"?null:(S=T&&S[T]||S["@@iterator"],typeof S=="function"?S:null)}var O={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},H=Object.assign,j={};function z(S,Y,$){this.props=S,this.context=Y,this.refs=j,this.updater=$||O}z.prototype.isReactComponent={},z.prototype.setState=function(S,Y){if(typeof S!="object"&&typeof S!="function"&&S!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,S,Y,"setState")},z.prototype.forceUpdate=function(S){this.updater.enqueueForceUpdate(this,S,"forceUpdate")};function V(){}V.prototype=z.prototype;function J(S,Y,$){this.props=S,this.context=Y,this.refs=j,this.updater=$||O}var U=J.prototype=new V;U.constructor=J,H(U,z.prototype),U.isPureReactComponent=!0;var X=Array.isArray,Z={H:null,A:null,T:null,S:null,V:null},le=Object.prototype.hasOwnProperty;function F(S,Y,$,Q,W,he){return $=he.ref,{$$typeof:i,type:S,key:Y,ref:$!==void 0?$:null,props:he}}function L(S,Y){return F(S.type,Y,void 0,void 0,void 0,S.props)}function ne(S){return typeof S=="object"&&S!==null&&S.$$typeof===i}function re(S){var Y={"=":"=0",":":"=2"};return"$"+S.replace(/[=:]/g,function($){return Y[$]})}var se=/\/+/g;function pe(S,Y){return typeof S=="object"&&S!==null&&S.key!=null?re(""+S.key):Y.toString(36)}function Ve(){}function ut(S){switch(S.status){case"fulfilled":return S.value;case"rejected":throw S.reason;default:switch(typeof S.status=="string"?S.then(Ve,Ve):(S.status="pending",S.then(function(Y){S.status==="pending"&&(S.status="fulfilled",S.value=Y)},function(Y){S.status==="pending"&&(S.status="rejected",S.reason=Y)})),S.status){case"fulfilled":return S.value;case"rejected":throw S.reason}}throw S}function Ne(S,Y,$,Q,W){var he=typeof S;(he==="undefined"||he==="boolean")&&(S=null);var ce=!1;if(S===null)ce=!0;else switch(he){case"bigint":case"string":case"number":ce=!0;break;case"object":switch(S.$$typeof){case i:case r:ce=!0;break;case E:return ce=S._init,Ne(ce(S._payload),Y,$,Q,W)}}if(ce)return W=W(S),ce=Q===""?"."+pe(S,0):Q,X(W)?($="",ce!=null&&($=ce.replace(se,"$&/")+"/"),Ne(W,Y,$,"",function(ht){return ht})):W!=null&&(ne(W)&&(W=L(W,$+(W.key==null||S&&S.key===W.key?"":(""+W.key).replace(se,"$&/")+"/")+ce)),Y.push(W)),1;ce=0;var xe=Q===""?".":Q+":";if(X(S))for(var Re=0;Re<S.length;Re++)Q=S[Re],he=xe+pe(Q,Re),ce+=Ne(Q,Y,$,he,W);else if(Re=R(S),typeof Re=="function")for(S=Re.call(S),Re=0;!(Q=S.next()).done;)Q=Q.value,he=xe+pe(Q,Re++),ce+=Ne(Q,Y,$,he,W);else if(he==="object"){if(typeof S.then=="function")return Ne(ut(S),Y,$,Q,W);throw Y=String(S),Error("Objects are not valid as a React child (found: "+(Y==="[object Object]"?"object with keys {"+Object.keys(S).join(", ")+"}":Y)+"). If you meant to render a collection of children, use an array instead.")}return ce}function D(S,Y,$){if(S==null)return S;var Q=[],W=0;return Ne(S,Q,"","",function(he){return Y.call($,he,W++)}),Q}function K(S){if(S._status===-1){var Y=S._result;Y=Y(),Y.then(function($){(S._status===0||S._status===-1)&&(S._status=1,S._result=$)},function($){(S._status===0||S._status===-1)&&(S._status=2,S._result=$)}),S._status===-1&&(S._status=0,S._result=Y)}if(S._status===1)return S._result.default;throw S._result}var G=typeof reportError=="function"?reportError:function(S){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var Y=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof S=="object"&&S!==null&&typeof S.message=="string"?String(S.message):String(S),error:S});if(!window.dispatchEvent(Y))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",S);return}console.error(S)};function be(){}return oe.Children={map:D,forEach:function(S,Y,$){D(S,function(){Y.apply(this,arguments)},$)},count:function(S){var Y=0;return D(S,function(){Y++}),Y},toArray:function(S){return D(S,function(Y){return Y})||[]},only:function(S){if(!ne(S))throw Error("React.Children.only expected to receive a single React element child.");return S}},oe.Component=z,oe.Fragment=s,oe.Profiler=d,oe.PureComponent=J,oe.StrictMode=c,oe.Suspense=x,oe.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=Z,oe.__COMPILER_RUNTIME={__proto__:null,c:function(S){return Z.H.useMemoCache(S)}},oe.cache=function(S){return function(){return S.apply(null,arguments)}},oe.cloneElement=function(S,Y,$){if(S==null)throw Error("The argument must be a React element, but you passed "+S+".");var Q=H({},S.props),W=S.key,he=void 0;if(Y!=null)for(ce in Y.ref!==void 0&&(he=void 0),Y.key!==void 0&&(W=""+Y.key),Y)!le.call(Y,ce)||ce==="key"||ce==="__self"||ce==="__source"||ce==="ref"&&Y.ref===void 0||(Q[ce]=Y[ce]);var ce=arguments.length-2;if(ce===1)Q.children=$;else if(1<ce){for(var xe=Array(ce),Re=0;Re<ce;Re++)xe[Re]=arguments[Re+2];Q.children=xe}return F(S.type,W,void 0,void 0,he,Q)},oe.createContext=function(S){return S={$$typeof:v,_currentValue:S,_currentValue2:S,_threadCount:0,Provider:null,Consumer:null},S.Provider=S,S.Consumer={$$typeof:h,_context:S},S},oe.createElement=function(S,Y,$){var Q,W={},he=null;if(Y!=null)for(Q in Y.key!==void 0&&(he=""+Y.key),Y)le.call(Y,Q)&&Q!=="key"&&Q!=="__self"&&Q!=="__source"&&(W[Q]=Y[Q]);var ce=arguments.length-2;if(ce===1)W.children=$;else if(1<ce){for(var xe=Array(ce),Re=0;Re<ce;Re++)xe[Re]=arguments[Re+2];W.children=xe}if(S&&S.defaultProps)for(Q in ce=S.defaultProps,ce)W[Q]===void 0&&(W[Q]=ce[Q]);return F(S,he,void 0,void 0,null,W)},oe.createRef=function(){return{current:null}},oe.forwardRef=function(S){return{$$typeof:g,render:S}},oe.isValidElement=ne,oe.lazy=function(S){return{$$typeof:E,_payload:{_status:-1,_result:S},_init:K}},oe.memo=function(S,Y){return{$$typeof:y,type:S,compare:Y===void 0?null:Y}},oe.startTransition=function(S){var Y=Z.T,$={};Z.T=$;try{var Q=S(),W=Z.S;W!==null&&W($,Q),typeof Q=="object"&&Q!==null&&typeof Q.then=="function"&&Q.then(be,G)}catch(he){G(he)}finally{Z.T=Y}},oe.unstable_useCacheRefresh=function(){return Z.H.useCacheRefresh()},oe.use=function(S){return Z.H.use(S)},oe.useActionState=function(S,Y,$){return Z.H.useActionState(S,Y,$)},oe.useCallback=function(S,Y){return Z.H.useCallback(S,Y)},oe.useContext=function(S){return Z.H.useContext(S)},oe.useDebugValue=function(){},oe.useDeferredValue=function(S,Y){return Z.H.useDeferredValue(S,Y)},oe.useEffect=function(S,Y,$){var Q=Z.H;if(typeof $=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Q.useEffect(S,Y)},oe.useId=function(){return Z.H.useId()},oe.useImperativeHandle=function(S,Y,$){return Z.H.useImperativeHandle(S,Y,$)},oe.useInsertionEffect=function(S,Y){return Z.H.useInsertionEffect(S,Y)},oe.useLayoutEffect=function(S,Y){return Z.H.useLayoutEffect(S,Y)},oe.useMemo=function(S,Y){return Z.H.useMemo(S,Y)},oe.useOptimistic=function(S,Y){return Z.H.useOptimistic(S,Y)},oe.useReducer=function(S,Y,$){return Z.H.useReducer(S,Y,$)},oe.useRef=function(S){return Z.H.useRef(S)},oe.useState=function(S){return Z.H.useState(S)},oe.useSyncExternalStore=function(S,Y,$){return Z.H.useSyncExternalStore(S,Y,$)},oe.useTransition=function(){return Z.H.useTransition()},oe.version="19.1.0",oe}var km;function Cs(){return km||(km=1,as.exports=Ry()),as.exports}var b=Cs();const Ll=hh(b),vh=jy({__proto__:null,default:Ll},[b]);var ns={exports:{}},ii={},is={exports:{}},us={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Bm;function wy(){return Bm||(Bm=1,function(i){function r(D,K){var G=D.length;D.push(K);e:for(;0<G;){var be=G-1>>>1,S=D[be];if(0<d(S,K))D[be]=K,D[G]=S,G=be;else break e}}function s(D){return D.length===0?null:D[0]}function c(D){if(D.length===0)return null;var K=D[0],G=D.pop();if(G!==K){D[0]=G;e:for(var be=0,S=D.length,Y=S>>>1;be<Y;){var $=2*(be+1)-1,Q=D[$],W=$+1,he=D[W];if(0>d(Q,G))W<S&&0>d(he,Q)?(D[be]=he,D[W]=G,be=W):(D[be]=Q,D[$]=G,be=$);else if(W<S&&0>d(he,G))D[be]=he,D[W]=G,be=W;else break e}}return K}function d(D,K){var G=D.sortIndex-K.sortIndex;return G!==0?G:D.id-K.id}if(i.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var h=performance;i.unstable_now=function(){return h.now()}}else{var v=Date,g=v.now();i.unstable_now=function(){return v.now()-g}}var x=[],y=[],E=1,T=null,R=3,O=!1,H=!1,j=!1,z=!1,V=typeof setTimeout=="function"?setTimeout:null,J=typeof clearTimeout=="function"?clearTimeout:null,U=typeof setImmediate<"u"?setImmediate:null;function X(D){for(var K=s(y);K!==null;){if(K.callback===null)c(y);else if(K.startTime<=D)c(y),K.sortIndex=K.expirationTime,r(x,K);else break;K=s(y)}}function Z(D){if(j=!1,X(D),!H)if(s(x)!==null)H=!0,le||(le=!0,pe());else{var K=s(y);K!==null&&Ne(Z,K.startTime-D)}}var le=!1,F=-1,L=5,ne=-1;function re(){return z?!0:!(i.unstable_now()-ne<L)}function se(){if(z=!1,le){var D=i.unstable_now();ne=D;var K=!0;try{e:{H=!1,j&&(j=!1,J(F),F=-1),O=!0;var G=R;try{t:{for(X(D),T=s(x);T!==null&&!(T.expirationTime>D&&re());){var be=T.callback;if(typeof be=="function"){T.callback=null,R=T.priorityLevel;var S=be(T.expirationTime<=D);if(D=i.unstable_now(),typeof S=="function"){T.callback=S,X(D),K=!0;break t}T===s(x)&&c(x),X(D)}else c(x);T=s(x)}if(T!==null)K=!0;else{var Y=s(y);Y!==null&&Ne(Z,Y.startTime-D),K=!1}}break e}finally{T=null,R=G,O=!1}K=void 0}}finally{K?pe():le=!1}}}var pe;if(typeof U=="function")pe=function(){U(se)};else if(typeof MessageChannel<"u"){var Ve=new MessageChannel,ut=Ve.port2;Ve.port1.onmessage=se,pe=function(){ut.postMessage(null)}}else pe=function(){V(se,0)};function Ne(D,K){F=V(function(){D(i.unstable_now())},K)}i.unstable_IdlePriority=5,i.unstable_ImmediatePriority=1,i.unstable_LowPriority=4,i.unstable_NormalPriority=3,i.unstable_Profiling=null,i.unstable_UserBlockingPriority=2,i.unstable_cancelCallback=function(D){D.callback=null},i.unstable_forceFrameRate=function(D){0>D||125<D?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):L=0<D?Math.floor(1e3/D):5},i.unstable_getCurrentPriorityLevel=function(){return R},i.unstable_next=function(D){switch(R){case 1:case 2:case 3:var K=3;break;default:K=R}var G=R;R=K;try{return D()}finally{R=G}},i.unstable_requestPaint=function(){z=!0},i.unstable_runWithPriority=function(D,K){switch(D){case 1:case 2:case 3:case 4:case 5:break;default:D=3}var G=R;R=D;try{return K()}finally{R=G}},i.unstable_scheduleCallback=function(D,K,G){var be=i.unstable_now();switch(typeof G=="object"&&G!==null?(G=G.delay,G=typeof G=="number"&&0<G?be+G:be):G=be,D){case 1:var S=-1;break;case 2:S=250;break;case 5:S=1073741823;break;case 4:S=1e4;break;default:S=5e3}return S=G+S,D={id:E++,callback:K,priorityLevel:D,startTime:G,expirationTime:S,sortIndex:-1},G>be?(D.sortIndex=G,r(y,D),s(x)===null&&D===s(y)&&(j?(J(F),F=-1):j=!0,Ne(Z,G-be))):(D.sortIndex=S,r(x,D),H||O||(H=!0,le||(le=!0,pe()))),D},i.unstable_shouldYield=re,i.unstable_wrapCallback=function(D){var K=R;return function(){var G=R;R=K;try{return D.apply(this,arguments)}finally{R=G}}}}(us)),us}var Lm;function Cy(){return Lm||(Lm=1,is.exports=wy()),is.exports}var cs={exports:{}},it={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qm;function Dy(){if(qm)return it;qm=1;var i=Cs();function r(x){var y="https://react.dev/errors/"+x;if(1<arguments.length){y+="?args[]="+encodeURIComponent(arguments[1]);for(var E=2;E<arguments.length;E++)y+="&args[]="+encodeURIComponent(arguments[E])}return"Minified React error #"+x+"; visit "+y+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function s(){}var c={d:{f:s,r:function(){throw Error(r(522))},D:s,C:s,L:s,m:s,X:s,S:s,M:s},p:0,findDOMNode:null},d=Symbol.for("react.portal");function h(x,y,E){var T=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:d,key:T==null?null:""+T,children:x,containerInfo:y,implementation:E}}var v=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function g(x,y){if(x==="font")return"";if(typeof y=="string")return y==="use-credentials"?y:""}return it.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=c,it.createPortal=function(x,y){var E=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!y||y.nodeType!==1&&y.nodeType!==9&&y.nodeType!==11)throw Error(r(299));return h(x,y,null,E)},it.flushSync=function(x){var y=v.T,E=c.p;try{if(v.T=null,c.p=2,x)return x()}finally{v.T=y,c.p=E,c.d.f()}},it.preconnect=function(x,y){typeof x=="string"&&(y?(y=y.crossOrigin,y=typeof y=="string"?y==="use-credentials"?y:"":void 0):y=null,c.d.C(x,y))},it.prefetchDNS=function(x){typeof x=="string"&&c.d.D(x)},it.preinit=function(x,y){if(typeof x=="string"&&y&&typeof y.as=="string"){var E=y.as,T=g(E,y.crossOrigin),R=typeof y.integrity=="string"?y.integrity:void 0,O=typeof y.fetchPriority=="string"?y.fetchPriority:void 0;E==="style"?c.d.S(x,typeof y.precedence=="string"?y.precedence:void 0,{crossOrigin:T,integrity:R,fetchPriority:O}):E==="script"&&c.d.X(x,{crossOrigin:T,integrity:R,fetchPriority:O,nonce:typeof y.nonce=="string"?y.nonce:void 0})}},it.preinitModule=function(x,y){if(typeof x=="string")if(typeof y=="object"&&y!==null){if(y.as==null||y.as==="script"){var E=g(y.as,y.crossOrigin);c.d.M(x,{crossOrigin:E,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0})}}else y==null&&c.d.M(x)},it.preload=function(x,y){if(typeof x=="string"&&typeof y=="object"&&y!==null&&typeof y.as=="string"){var E=y.as,T=g(E,y.crossOrigin);c.d.L(x,E,{crossOrigin:T,integrity:typeof y.integrity=="string"?y.integrity:void 0,nonce:typeof y.nonce=="string"?y.nonce:void 0,type:typeof y.type=="string"?y.type:void 0,fetchPriority:typeof y.fetchPriority=="string"?y.fetchPriority:void 0,referrerPolicy:typeof y.referrerPolicy=="string"?y.referrerPolicy:void 0,imageSrcSet:typeof y.imageSrcSet=="string"?y.imageSrcSet:void 0,imageSizes:typeof y.imageSizes=="string"?y.imageSizes:void 0,media:typeof y.media=="string"?y.media:void 0})}},it.preloadModule=function(x,y){if(typeof x=="string")if(y){var E=g(y.as,y.crossOrigin);c.d.m(x,{as:typeof y.as=="string"&&y.as!=="script"?y.as:void 0,crossOrigin:E,integrity:typeof y.integrity=="string"?y.integrity:void 0})}else c.d.m(x)},it.requestFormReset=function(x){c.d.r(x)},it.unstable_batchedUpdates=function(x,y){return x(y)},it.useFormState=function(x,y,E){return v.H.useFormState(x,y,E)},it.useFormStatus=function(){return v.H.useHostTransitionStatus()},it.version="19.1.0",it}var Gm;function gh(){if(Gm)return cs.exports;Gm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),cs.exports=Dy(),cs.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Ym;function Oy(){if(Ym)return ii;Ym=1;var i=Cy(),r=Cs(),s=gh();function c(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var l=2;l<arguments.length;l++)t+="&args[]="+encodeURIComponent(arguments[l])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function d(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function h(e){var t=e,l=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(l=t.return),e=t.return;while(e)}return t.tag===3?l:null}function v(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function g(e){if(h(e)!==e)throw Error(c(188))}function x(e){var t=e.alternate;if(!t){if(t=h(e),t===null)throw Error(c(188));return t!==e?null:e}for(var l=e,a=t;;){var n=l.return;if(n===null)break;var u=n.alternate;if(u===null){if(a=n.return,a!==null){l=a;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===l)return g(n),e;if(u===a)return g(n),t;u=u.sibling}throw Error(c(188))}if(l.return!==a.return)l=n,a=u;else{for(var o=!1,m=n.child;m;){if(m===l){o=!0,l=n,a=u;break}if(m===a){o=!0,a=n,l=u;break}m=m.sibling}if(!o){for(m=u.child;m;){if(m===l){o=!0,l=u,a=n;break}if(m===a){o=!0,a=u,l=n;break}m=m.sibling}if(!o)throw Error(c(189))}}if(l.alternate!==a)throw Error(c(190))}if(l.tag!==3)throw Error(c(188));return l.stateNode.current===l?e:t}function y(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=y(e),t!==null)return t;e=e.sibling}return null}var E=Object.assign,T=Symbol.for("react.element"),R=Symbol.for("react.transitional.element"),O=Symbol.for("react.portal"),H=Symbol.for("react.fragment"),j=Symbol.for("react.strict_mode"),z=Symbol.for("react.profiler"),V=Symbol.for("react.provider"),J=Symbol.for("react.consumer"),U=Symbol.for("react.context"),X=Symbol.for("react.forward_ref"),Z=Symbol.for("react.suspense"),le=Symbol.for("react.suspense_list"),F=Symbol.for("react.memo"),L=Symbol.for("react.lazy"),ne=Symbol.for("react.activity"),re=Symbol.for("react.memo_cache_sentinel"),se=Symbol.iterator;function pe(e){return e===null||typeof e!="object"?null:(e=se&&e[se]||e["@@iterator"],typeof e=="function"?e:null)}var Ve=Symbol.for("react.client.reference");function ut(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===Ve?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case H:return"Fragment";case z:return"Profiler";case j:return"StrictMode";case Z:return"Suspense";case le:return"SuspenseList";case ne:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case O:return"Portal";case U:return(e.displayName||"Context")+".Provider";case J:return(e._context.displayName||"Context")+".Consumer";case X:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case F:return t=e.displayName||null,t!==null?t:ut(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return ut(e(t))}catch{}}return null}var Ne=Array.isArray,D=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,K=s.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,G={pending:!1,data:null,method:null,action:null},be=[],S=-1;function Y(e){return{current:e}}function $(e){0>S||(e.current=be[S],be[S]=null,S--)}function Q(e,t){S++,be[S]=e.current,e.current=t}var W=Y(null),he=Y(null),ce=Y(null),xe=Y(null);function Re(e,t){switch(Q(ce,t),Q(he,e),Q(W,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?rm(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=rm(t),e=sm(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}$(W),Q(W,e)}function ht(){$(W),$(he),$(ce)}function fl(e){e.memoizedState!==null&&Q(xe,e);var t=W.current,l=sm(t,e.type);t!==l&&(Q(he,e),Q(W,l))}function dl(e){he.current===e&&($(W),$(he)),xe.current===e&&($(xe),In._currentValue=G)}var ml=Object.prototype.hasOwnProperty,Vu=i.unstable_scheduleCallback,Xu=i.unstable_cancelCallback,ng=i.unstable_shouldYield,ig=i.unstable_requestPaint,qt=i.unstable_now,ug=i.unstable_getCurrentPriorityLevel,qs=i.unstable_ImmediatePriority,Gs=i.unstable_UserBlockingPriority,fi=i.unstable_NormalPriority,cg=i.unstable_LowPriority,Ys=i.unstable_IdlePriority,rg=i.log,sg=i.unstable_setDisableYieldValue,cn=null,vt=null;function hl(e){if(typeof rg=="function"&&sg(e),vt&&typeof vt.setStrictMode=="function")try{vt.setStrictMode(cn,e)}catch{}}var gt=Math.clz32?Math.clz32:dg,og=Math.log,fg=Math.LN2;function dg(e){return e>>>=0,e===0?32:31-(og(e)/fg|0)|0}var di=256,mi=4194304;function Yl(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function hi(e,t,l){var a=e.pendingLanes;if(a===0)return 0;var n=0,u=e.suspendedLanes,o=e.pingedLanes;e=e.warmLanes;var m=a&134217727;return m!==0?(a=m&~u,a!==0?n=Yl(a):(o&=m,o!==0?n=Yl(o):l||(l=m&~e,l!==0&&(n=Yl(l))))):(m=a&~u,m!==0?n=Yl(m):o!==0?n=Yl(o):l||(l=a&~e,l!==0&&(n=Yl(l)))),n===0?0:t!==0&&t!==n&&(t&u)===0&&(u=n&-n,l=t&-t,u>=l||u===32&&(l&4194048)!==0)?t:n}function rn(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function mg(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Vs(){var e=di;return di<<=1,(di&4194048)===0&&(di=256),e}function Xs(){var e=mi;return mi<<=1,(mi&62914560)===0&&(mi=4194304),e}function Qu(e){for(var t=[],l=0;31>l;l++)t.push(e);return t}function sn(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function hg(e,t,l,a,n,u){var o=e.pendingLanes;e.pendingLanes=l,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=l,e.entangledLanes&=l,e.errorRecoveryDisabledLanes&=l,e.shellSuspendCounter=0;var m=e.entanglements,p=e.expirationTimes,M=e.hiddenUpdates;for(l=o&~l;0<l;){var k=31-gt(l),q=1<<k;m[k]=0,p[k]=-1;var w=M[k];if(w!==null)for(M[k]=null,k=0;k<w.length;k++){var C=w[k];C!==null&&(C.lane&=-536870913)}l&=~q}a!==0&&Qs(e,a,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(o&~t))}function Qs(e,t,l){e.pendingLanes|=t,e.suspendedLanes&=~t;var a=31-gt(t);e.entangledLanes|=t,e.entanglements[a]=e.entanglements[a]|1073741824|l&4194090}function Zs(e,t){var l=e.entangledLanes|=t;for(e=e.entanglements;l;){var a=31-gt(l),n=1<<a;n&t|e[a]&t&&(e[a]|=t),l&=~n}}function Zu(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Ku(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function Ks(){var e=K.p;return e!==0?e:(e=window.event,e===void 0?32:Mm(e.type))}function vg(e,t){var l=K.p;try{return K.p=e,t()}finally{K.p=l}}var vl=Math.random().toString(36).slice(2),at="__reactFiber$"+vl,st="__reactProps$"+vl,ra="__reactContainer$"+vl,Ju="__reactEvents$"+vl,gg="__reactListeners$"+vl,yg="__reactHandles$"+vl,Js="__reactResources$"+vl,on="__reactMarker$"+vl;function $u(e){delete e[at],delete e[st],delete e[Ju],delete e[gg],delete e[yg]}function sa(e){var t=e[at];if(t)return t;for(var l=e.parentNode;l;){if(t=l[ra]||l[at]){if(l=t.alternate,t.child!==null||l!==null&&l.child!==null)for(e=mm(e);e!==null;){if(l=e[at])return l;e=mm(e)}return t}e=l,l=e.parentNode}return null}function oa(e){if(e=e[at]||e[ra]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function fn(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(c(33))}function fa(e){var t=e[Js];return t||(t=e[Js]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function $e(e){e[on]=!0}var $s=new Set,Ws={};function Vl(e,t){da(e,t),da(e+"Capture",t)}function da(e,t){for(Ws[e]=t,e=0;e<t.length;e++)$s.add(t[e])}var pg=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Fs={},Ps={};function bg(e){return ml.call(Ps,e)?!0:ml.call(Fs,e)?!1:pg.test(e)?Ps[e]=!0:(Fs[e]=!0,!1)}function vi(e,t,l){if(bg(t))if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var a=t.toLowerCase().slice(0,5);if(a!=="data-"&&a!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+l)}}function gi(e,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+l)}}function Jt(e,t,l,a){if(a===null)e.removeAttribute(l);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(l);return}e.setAttributeNS(t,l,""+a)}}var Wu,Is;function ma(e){if(Wu===void 0)try{throw Error()}catch(l){var t=l.stack.trim().match(/\n( *(at )?)/);Wu=t&&t[1]||"",Is=-1<l.stack.indexOf(`
    at`)?" (<anonymous>)":-1<l.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Wu+e+Is}var Fu=!1;function Pu(e,t){if(!e||Fu)return"";Fu=!0;var l=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var a={DetermineComponentFrameRoot:function(){try{if(t){var q=function(){throw Error()};if(Object.defineProperty(q.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(q,[])}catch(C){var w=C}Reflect.construct(e,[],q)}else{try{q.call()}catch(C){w=C}e.call(q.prototype)}}else{try{throw Error()}catch(C){w=C}(q=e())&&typeof q.catch=="function"&&q.catch(function(){})}}catch(C){if(C&&w&&typeof C.stack=="string")return[C.stack,w.stack]}return[null,null]}};a.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(a.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(a.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=a.DetermineComponentFrameRoot(),o=u[0],m=u[1];if(o&&m){var p=o.split(`
`),M=m.split(`
`);for(n=a=0;a<p.length&&!p[a].includes("DetermineComponentFrameRoot");)a++;for(;n<M.length&&!M[n].includes("DetermineComponentFrameRoot");)n++;if(a===p.length||n===M.length)for(a=p.length-1,n=M.length-1;1<=a&&0<=n&&p[a]!==M[n];)n--;for(;1<=a&&0<=n;a--,n--)if(p[a]!==M[n]){if(a!==1||n!==1)do if(a--,n--,0>n||p[a]!==M[n]){var k=`
`+p[a].replace(" at new "," at ");return e.displayName&&k.includes("<anonymous>")&&(k=k.replace("<anonymous>",e.displayName)),k}while(1<=a&&0<=n);break}}}finally{Fu=!1,Error.prepareStackTrace=l}return(l=e?e.displayName||e.name:"")?ma(l):""}function xg(e){switch(e.tag){case 26:case 27:case 5:return ma(e.type);case 16:return ma("Lazy");case 13:return ma("Suspense");case 19:return ma("SuspenseList");case 0:case 15:return Pu(e.type,!1);case 11:return Pu(e.type.render,!1);case 1:return Pu(e.type,!0);case 31:return ma("Activity");default:return""}}function eo(e){try{var t="";do t+=xg(e),e=e.return;while(e);return t}catch(l){return`
Error generating stack: `+l.message+`
`+l.stack}}function At(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function to(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Sg(e){var t=to(e)?"checked":"value",l=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),a=""+e[t];if(!e.hasOwnProperty(t)&&typeof l<"u"&&typeof l.get=="function"&&typeof l.set=="function"){var n=l.get,u=l.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(o){a=""+o,u.call(this,o)}}),Object.defineProperty(e,t,{enumerable:l.enumerable}),{getValue:function(){return a},setValue:function(o){a=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function yi(e){e._valueTracker||(e._valueTracker=Sg(e))}function lo(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var l=t.getValue(),a="";return e&&(a=to(e)?e.checked?"true":"false":e.value),e=a,e!==l?(t.setValue(e),!0):!1}function pi(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Eg=/[\n"\\]/g;function jt(e){return e.replace(Eg,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Iu(e,t,l,a,n,u,o,m){e.name="",o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.type=o:e.removeAttribute("type"),t!=null?o==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+At(t)):e.value!==""+At(t)&&(e.value=""+At(t)):o!=="submit"&&o!=="reset"||e.removeAttribute("value"),t!=null?ec(e,o,At(t)):l!=null?ec(e,o,At(l)):a!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),m!=null&&typeof m!="function"&&typeof m!="symbol"&&typeof m!="boolean"?e.name=""+At(m):e.removeAttribute("name")}function ao(e,t,l,a,n,u,o,m){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||l!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;l=l!=null?""+At(l):"",t=t!=null?""+At(t):l,m||t===e.value||(e.value=t),e.defaultValue=t}a=a??n,a=typeof a!="function"&&typeof a!="symbol"&&!!a,e.checked=m?e.checked:!!a,e.defaultChecked=!!a,o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"&&(e.name=o)}function ec(e,t,l){t==="number"&&pi(e.ownerDocument)===e||e.defaultValue===""+l||(e.defaultValue=""+l)}function ha(e,t,l,a){if(e=e.options,t){t={};for(var n=0;n<l.length;n++)t["$"+l[n]]=!0;for(l=0;l<e.length;l++)n=t.hasOwnProperty("$"+e[l].value),e[l].selected!==n&&(e[l].selected=n),n&&a&&(e[l].defaultSelected=!0)}else{for(l=""+At(l),t=null,n=0;n<e.length;n++){if(e[n].value===l){e[n].selected=!0,a&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function no(e,t,l){if(t!=null&&(t=""+At(t),t!==e.value&&(e.value=t),l==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=l!=null?""+At(l):""}function io(e,t,l,a){if(t==null){if(a!=null){if(l!=null)throw Error(c(92));if(Ne(a)){if(1<a.length)throw Error(c(93));a=a[0]}l=a}l==null&&(l=""),t=l}l=At(t),e.defaultValue=l,a=e.textContent,a===l&&a!==""&&a!==null&&(e.value=a)}function va(e,t){if(t){var l=e.firstChild;if(l&&l===e.lastChild&&l.nodeType===3){l.nodeValue=t;return}}e.textContent=t}var Ng=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function uo(e,t,l){var a=t.indexOf("--")===0;l==null||typeof l=="boolean"||l===""?a?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":a?e.setProperty(t,l):typeof l!="number"||l===0||Ng.has(t)?t==="float"?e.cssFloat=l:e[t]=(""+l).trim():e[t]=l+"px"}function co(e,t,l){if(t!=null&&typeof t!="object")throw Error(c(62));if(e=e.style,l!=null){for(var a in l)!l.hasOwnProperty(a)||t!=null&&t.hasOwnProperty(a)||(a.indexOf("--")===0?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="");for(var n in t)a=t[n],t.hasOwnProperty(n)&&l[n]!==a&&uo(e,n,a)}else for(var u in t)t.hasOwnProperty(u)&&uo(e,u,t[u])}function tc(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Tg=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Ag=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function bi(e){return Ag.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var lc=null;function ac(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var ga=null,ya=null;function ro(e){var t=oa(e);if(t&&(e=t.stateNode)){var l=e[st]||null;e:switch(e=t.stateNode,t.type){case"input":if(Iu(e,l.value,l.defaultValue,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name),t=l.name,l.type==="radio"&&t!=null){for(l=e;l.parentNode;)l=l.parentNode;for(l=l.querySelectorAll('input[name="'+jt(""+t)+'"][type="radio"]'),t=0;t<l.length;t++){var a=l[t];if(a!==e&&a.form===e.form){var n=a[st]||null;if(!n)throw Error(c(90));Iu(a,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<l.length;t++)a=l[t],a.form===e.form&&lo(a)}break e;case"textarea":no(e,l.value,l.defaultValue);break e;case"select":t=l.value,t!=null&&ha(e,!!l.multiple,t,!1)}}}var nc=!1;function so(e,t,l){if(nc)return e(t,l);nc=!0;try{var a=e(t);return a}finally{if(nc=!1,(ga!==null||ya!==null)&&(nu(),ga&&(t=ga,e=ya,ya=ga=null,ro(t),e)))for(t=0;t<e.length;t++)ro(e[t])}}function dn(e,t){var l=e.stateNode;if(l===null)return null;var a=l[st]||null;if(a===null)return null;l=a[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(a=!a.disabled)||(e=e.type,a=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!a;break e;default:e=!1}if(e)return null;if(l&&typeof l!="function")throw Error(c(231,t,typeof l));return l}var $t=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),ic=!1;if($t)try{var mn={};Object.defineProperty(mn,"passive",{get:function(){ic=!0}}),window.addEventListener("test",mn,mn),window.removeEventListener("test",mn,mn)}catch{ic=!1}var gl=null,uc=null,xi=null;function oo(){if(xi)return xi;var e,t=uc,l=t.length,a,n="value"in gl?gl.value:gl.textContent,u=n.length;for(e=0;e<l&&t[e]===n[e];e++);var o=l-e;for(a=1;a<=o&&t[l-a]===n[u-a];a++);return xi=n.slice(e,1<a?1-a:void 0)}function Si(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ei(){return!0}function fo(){return!1}function ot(e){function t(l,a,n,u,o){this._reactName=l,this._targetInst=n,this.type=a,this.nativeEvent=u,this.target=o,this.currentTarget=null;for(var m in e)e.hasOwnProperty(m)&&(l=e[m],this[m]=l?l(u):u[m]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Ei:fo,this.isPropagationStopped=fo,this}return E(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var l=this.nativeEvent;l&&(l.preventDefault?l.preventDefault():typeof l.returnValue!="unknown"&&(l.returnValue=!1),this.isDefaultPrevented=Ei)},stopPropagation:function(){var l=this.nativeEvent;l&&(l.stopPropagation?l.stopPropagation():typeof l.cancelBubble!="unknown"&&(l.cancelBubble=!0),this.isPropagationStopped=Ei)},persist:function(){},isPersistent:Ei}),t}var Xl={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ni=ot(Xl),hn=E({},Xl,{view:0,detail:0}),jg=ot(hn),cc,rc,vn,Ti=E({},hn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:oc,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==vn&&(vn&&e.type==="mousemove"?(cc=e.screenX-vn.screenX,rc=e.screenY-vn.screenY):rc=cc=0,vn=e),cc)},movementY:function(e){return"movementY"in e?e.movementY:rc}}),mo=ot(Ti),_g=E({},Ti,{dataTransfer:0}),Mg=ot(_g),Rg=E({},hn,{relatedTarget:0}),sc=ot(Rg),wg=E({},Xl,{animationName:0,elapsedTime:0,pseudoElement:0}),Cg=ot(wg),Dg=E({},Xl,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Og=ot(Dg),zg=E({},Xl,{data:0}),ho=ot(zg),Ug={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Hg={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},kg={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Bg(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=kg[e])?!!t[e]:!1}function oc(){return Bg}var Lg=E({},hn,{key:function(e){if(e.key){var t=Ug[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Si(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Hg[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:oc,charCode:function(e){return e.type==="keypress"?Si(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Si(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),qg=ot(Lg),Gg=E({},Ti,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),vo=ot(Gg),Yg=E({},hn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:oc}),Vg=ot(Yg),Xg=E({},Xl,{propertyName:0,elapsedTime:0,pseudoElement:0}),Qg=ot(Xg),Zg=E({},Ti,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Kg=ot(Zg),Jg=E({},Xl,{newState:0,oldState:0}),$g=ot(Jg),Wg=[9,13,27,32],fc=$t&&"CompositionEvent"in window,gn=null;$t&&"documentMode"in document&&(gn=document.documentMode);var Fg=$t&&"TextEvent"in window&&!gn,go=$t&&(!fc||gn&&8<gn&&11>=gn),yo=" ",po=!1;function bo(e,t){switch(e){case"keyup":return Wg.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function xo(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var pa=!1;function Pg(e,t){switch(e){case"compositionend":return xo(t);case"keypress":return t.which!==32?null:(po=!0,yo);case"textInput":return e=t.data,e===yo&&po?null:e;default:return null}}function Ig(e,t){if(pa)return e==="compositionend"||!fc&&bo(e,t)?(e=oo(),xi=uc=gl=null,pa=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return go&&t.locale!=="ko"?null:t.data;default:return null}}var e0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function So(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!e0[e.type]:t==="textarea"}function Eo(e,t,l,a){ga?ya?ya.push(a):ya=[a]:ga=a,t=ou(t,"onChange"),0<t.length&&(l=new Ni("onChange","change",null,l,a),e.push({event:l,listeners:t}))}var yn=null,pn=null;function t0(e){am(e,0)}function Ai(e){var t=fn(e);if(lo(t))return e}function No(e,t){if(e==="change")return t}var To=!1;if($t){var dc;if($t){var mc="oninput"in document;if(!mc){var Ao=document.createElement("div");Ao.setAttribute("oninput","return;"),mc=typeof Ao.oninput=="function"}dc=mc}else dc=!1;To=dc&&(!document.documentMode||9<document.documentMode)}function jo(){yn&&(yn.detachEvent("onpropertychange",_o),pn=yn=null)}function _o(e){if(e.propertyName==="value"&&Ai(pn)){var t=[];Eo(t,pn,e,ac(e)),so(t0,t)}}function l0(e,t,l){e==="focusin"?(jo(),yn=t,pn=l,yn.attachEvent("onpropertychange",_o)):e==="focusout"&&jo()}function a0(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ai(pn)}function n0(e,t){if(e==="click")return Ai(t)}function i0(e,t){if(e==="input"||e==="change")return Ai(t)}function u0(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yt=typeof Object.is=="function"?Object.is:u0;function bn(e,t){if(yt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var l=Object.keys(e),a=Object.keys(t);if(l.length!==a.length)return!1;for(a=0;a<l.length;a++){var n=l[a];if(!ml.call(t,n)||!yt(e[n],t[n]))return!1}return!0}function Mo(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ro(e,t){var l=Mo(e);e=0;for(var a;l;){if(l.nodeType===3){if(a=e+l.textContent.length,e<=t&&a>=t)return{node:l,offset:t-e};e=a}e:{for(;l;){if(l.nextSibling){l=l.nextSibling;break e}l=l.parentNode}l=void 0}l=Mo(l)}}function wo(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?wo(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Co(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=pi(e.document);t instanceof e.HTMLIFrameElement;){try{var l=typeof t.contentWindow.location.href=="string"}catch{l=!1}if(l)e=t.contentWindow;else break;t=pi(e.document)}return t}function hc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var c0=$t&&"documentMode"in document&&11>=document.documentMode,ba=null,vc=null,xn=null,gc=!1;function Do(e,t,l){var a=l.window===l?l.document:l.nodeType===9?l:l.ownerDocument;gc||ba==null||ba!==pi(a)||(a=ba,"selectionStart"in a&&hc(a)?a={start:a.selectionStart,end:a.selectionEnd}:(a=(a.ownerDocument&&a.ownerDocument.defaultView||window).getSelection(),a={anchorNode:a.anchorNode,anchorOffset:a.anchorOffset,focusNode:a.focusNode,focusOffset:a.focusOffset}),xn&&bn(xn,a)||(xn=a,a=ou(vc,"onSelect"),0<a.length&&(t=new Ni("onSelect","select",null,t,l),e.push({event:t,listeners:a}),t.target=ba)))}function Ql(e,t){var l={};return l[e.toLowerCase()]=t.toLowerCase(),l["Webkit"+e]="webkit"+t,l["Moz"+e]="moz"+t,l}var xa={animationend:Ql("Animation","AnimationEnd"),animationiteration:Ql("Animation","AnimationIteration"),animationstart:Ql("Animation","AnimationStart"),transitionrun:Ql("Transition","TransitionRun"),transitionstart:Ql("Transition","TransitionStart"),transitioncancel:Ql("Transition","TransitionCancel"),transitionend:Ql("Transition","TransitionEnd")},yc={},Oo={};$t&&(Oo=document.createElement("div").style,"AnimationEvent"in window||(delete xa.animationend.animation,delete xa.animationiteration.animation,delete xa.animationstart.animation),"TransitionEvent"in window||delete xa.transitionend.transition);function Zl(e){if(yc[e])return yc[e];if(!xa[e])return e;var t=xa[e],l;for(l in t)if(t.hasOwnProperty(l)&&l in Oo)return yc[e]=t[l];return e}var zo=Zl("animationend"),Uo=Zl("animationiteration"),Ho=Zl("animationstart"),r0=Zl("transitionrun"),s0=Zl("transitionstart"),o0=Zl("transitioncancel"),ko=Zl("transitionend"),Bo=new Map,pc="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");pc.push("scrollEnd");function zt(e,t){Bo.set(e,t),Vl(t,[e])}var Lo=new WeakMap;function _t(e,t){if(typeof e=="object"&&e!==null){var l=Lo.get(e);return l!==void 0?l:(t={value:e,source:t,stack:eo(t)},Lo.set(e,t),t)}return{value:e,source:t,stack:eo(t)}}var Mt=[],Sa=0,bc=0;function ji(){for(var e=Sa,t=bc=Sa=0;t<e;){var l=Mt[t];Mt[t++]=null;var a=Mt[t];Mt[t++]=null;var n=Mt[t];Mt[t++]=null;var u=Mt[t];if(Mt[t++]=null,a!==null&&n!==null){var o=a.pending;o===null?n.next=n:(n.next=o.next,o.next=n),a.pending=n}u!==0&&qo(l,n,u)}}function _i(e,t,l,a){Mt[Sa++]=e,Mt[Sa++]=t,Mt[Sa++]=l,Mt[Sa++]=a,bc|=a,e.lanes|=a,e=e.alternate,e!==null&&(e.lanes|=a)}function xc(e,t,l,a){return _i(e,t,l,a),Mi(e)}function Ea(e,t){return _i(e,null,null,t),Mi(e)}function qo(e,t,l){e.lanes|=l;var a=e.alternate;a!==null&&(a.lanes|=l);for(var n=!1,u=e.return;u!==null;)u.childLanes|=l,a=u.alternate,a!==null&&(a.childLanes|=l),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,n&&t!==null&&(n=31-gt(l),e=u.hiddenUpdates,a=e[n],a===null?e[n]=[t]:a.push(t),t.lane=l|536870912),u):null}function Mi(e){if(50<Qn)throw Qn=0,jr=null,Error(c(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Na={};function f0(e,t,l,a){this.tag=e,this.key=l,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=a,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function pt(e,t,l,a){return new f0(e,t,l,a)}function Sc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Wt(e,t){var l=e.alternate;return l===null?(l=pt(e.tag,t,e.key,e.mode),l.elementType=e.elementType,l.type=e.type,l.stateNode=e.stateNode,l.alternate=e,e.alternate=l):(l.pendingProps=t,l.type=e.type,l.flags=0,l.subtreeFlags=0,l.deletions=null),l.flags=e.flags&65011712,l.childLanes=e.childLanes,l.lanes=e.lanes,l.child=e.child,l.memoizedProps=e.memoizedProps,l.memoizedState=e.memoizedState,l.updateQueue=e.updateQueue,t=e.dependencies,l.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},l.sibling=e.sibling,l.index=e.index,l.ref=e.ref,l.refCleanup=e.refCleanup,l}function Go(e,t){e.flags&=65011714;var l=e.alternate;return l===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=l.childLanes,e.lanes=l.lanes,e.child=l.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=l.memoizedProps,e.memoizedState=l.memoizedState,e.updateQueue=l.updateQueue,e.type=l.type,t=l.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ri(e,t,l,a,n,u){var o=0;if(a=e,typeof e=="function")Sc(e)&&(o=1);else if(typeof e=="string")o=my(e,l,W.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case ne:return e=pt(31,l,t,n),e.elementType=ne,e.lanes=u,e;case H:return Kl(l.children,n,u,t);case j:o=8,n|=24;break;case z:return e=pt(12,l,t,n|2),e.elementType=z,e.lanes=u,e;case Z:return e=pt(13,l,t,n),e.elementType=Z,e.lanes=u,e;case le:return e=pt(19,l,t,n),e.elementType=le,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case V:case U:o=10;break e;case J:o=9;break e;case X:o=11;break e;case F:o=14;break e;case L:o=16,a=null;break e}o=29,l=Error(c(130,e===null?"null":typeof e,"")),a=null}return t=pt(o,l,t,n),t.elementType=e,t.type=a,t.lanes=u,t}function Kl(e,t,l,a){return e=pt(7,e,a,t),e.lanes=l,e}function Ec(e,t,l){return e=pt(6,e,null,t),e.lanes=l,e}function Nc(e,t,l){return t=pt(4,e.children!==null?e.children:[],e.key,t),t.lanes=l,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Ta=[],Aa=0,wi=null,Ci=0,Rt=[],wt=0,Jl=null,Ft=1,Pt="";function $l(e,t){Ta[Aa++]=Ci,Ta[Aa++]=wi,wi=e,Ci=t}function Yo(e,t,l){Rt[wt++]=Ft,Rt[wt++]=Pt,Rt[wt++]=Jl,Jl=e;var a=Ft;e=Pt;var n=32-gt(a)-1;a&=~(1<<n),l+=1;var u=32-gt(t)+n;if(30<u){var o=n-n%5;u=(a&(1<<o)-1).toString(32),a>>=o,n-=o,Ft=1<<32-gt(t)+n|l<<n|a,Pt=u+e}else Ft=1<<u|l<<n|a,Pt=e}function Tc(e){e.return!==null&&($l(e,1),Yo(e,1,0))}function Ac(e){for(;e===wi;)wi=Ta[--Aa],Ta[Aa]=null,Ci=Ta[--Aa],Ta[Aa]=null;for(;e===Jl;)Jl=Rt[--wt],Rt[wt]=null,Pt=Rt[--wt],Rt[wt]=null,Ft=Rt[--wt],Rt[wt]=null}var ct=null,ke=null,Ee=!1,Wl=null,Gt=!1,jc=Error(c(519));function Fl(e){var t=Error(c(418,""));throw Nn(_t(t,e)),jc}function Vo(e){var t=e.stateNode,l=e.type,a=e.memoizedProps;switch(t[at]=e,t[st]=a,l){case"dialog":ge("cancel",t),ge("close",t);break;case"iframe":case"object":case"embed":ge("load",t);break;case"video":case"audio":for(l=0;l<Kn.length;l++)ge(Kn[l],t);break;case"source":ge("error",t);break;case"img":case"image":case"link":ge("error",t),ge("load",t);break;case"details":ge("toggle",t);break;case"input":ge("invalid",t),ao(t,a.value,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name,!0),yi(t);break;case"select":ge("invalid",t);break;case"textarea":ge("invalid",t),io(t,a.value,a.defaultValue,a.children),yi(t)}l=a.children,typeof l!="string"&&typeof l!="number"&&typeof l!="bigint"||t.textContent===""+l||a.suppressHydrationWarning===!0||cm(t.textContent,l)?(a.popover!=null&&(ge("beforetoggle",t),ge("toggle",t)),a.onScroll!=null&&ge("scroll",t),a.onScrollEnd!=null&&ge("scrollend",t),a.onClick!=null&&(t.onclick=fu),t=!0):t=!1,t||Fl(e)}function Xo(e){for(ct=e.return;ct;)switch(ct.tag){case 5:case 13:Gt=!1;return;case 27:case 3:Gt=!0;return;default:ct=ct.return}}function Sn(e){if(e!==ct)return!1;if(!Ee)return Xo(e),Ee=!0,!1;var t=e.tag,l;if((l=t!==3&&t!==27)&&((l=t===5)&&(l=e.type,l=!(l!=="form"&&l!=="button")||Yr(e.type,e.memoizedProps)),l=!l),l&&ke&&Fl(e),Xo(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(c(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(l=e.data,l==="/$"){if(t===0){ke=Ht(e.nextSibling);break e}t--}else l!=="$"&&l!=="$!"&&l!=="$?"||t++;e=e.nextSibling}ke=null}}else t===27?(t=ke,Dl(e.type)?(e=Zr,Zr=null,ke=e):ke=t):ke=ct?Ht(e.stateNode.nextSibling):null;return!0}function En(){ke=ct=null,Ee=!1}function Qo(){var e=Wl;return e!==null&&(mt===null?mt=e:mt.push.apply(mt,e),Wl=null),e}function Nn(e){Wl===null?Wl=[e]:Wl.push(e)}var _c=Y(null),Pl=null,It=null;function yl(e,t,l){Q(_c,t._currentValue),t._currentValue=l}function el(e){e._currentValue=_c.current,$(_c)}function Mc(e,t,l){for(;e!==null;){var a=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,a!==null&&(a.childLanes|=t)):a!==null&&(a.childLanes&t)!==t&&(a.childLanes|=t),e===l)break;e=e.return}}function Rc(e,t,l,a){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var o=n.child;u=u.firstContext;e:for(;u!==null;){var m=u;u=n;for(var p=0;p<t.length;p++)if(m.context===t[p]){u.lanes|=l,m=u.alternate,m!==null&&(m.lanes|=l),Mc(u.return,l,e),a||(o=null);break e}u=m.next}}else if(n.tag===18){if(o=n.return,o===null)throw Error(c(341));o.lanes|=l,u=o.alternate,u!==null&&(u.lanes|=l),Mc(o,l,e),o=null}else o=n.child;if(o!==null)o.return=n;else for(o=n;o!==null;){if(o===e){o=null;break}if(n=o.sibling,n!==null){n.return=o.return,o=n;break}o=o.return}n=o}}function Tn(e,t,l,a){e=null;for(var n=t,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var o=n.alternate;if(o===null)throw Error(c(387));if(o=o.memoizedProps,o!==null){var m=n.type;yt(n.pendingProps.value,o.value)||(e!==null?e.push(m):e=[m])}}else if(n===xe.current){if(o=n.alternate,o===null)throw Error(c(387));o.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(In):e=[In])}n=n.return}e!==null&&Rc(t,e,l,a),t.flags|=262144}function Di(e){for(e=e.firstContext;e!==null;){if(!yt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Il(e){Pl=e,It=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function nt(e){return Zo(Pl,e)}function Oi(e,t){return Pl===null&&Il(e),Zo(e,t)}function Zo(e,t){var l=t._currentValue;if(t={context:t,memoizedValue:l,next:null},It===null){if(e===null)throw Error(c(308));It=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else It=It.next=t;return l}var d0=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(l,a){e.push(a)}};this.abort=function(){t.aborted=!0,e.forEach(function(l){return l()})}},m0=i.unstable_scheduleCallback,h0=i.unstable_NormalPriority,Xe={$$typeof:U,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function wc(){return{controller:new d0,data:new Map,refCount:0}}function An(e){e.refCount--,e.refCount===0&&m0(h0,function(){e.controller.abort()})}var jn=null,Cc=0,ja=0,_a=null;function v0(e,t){if(jn===null){var l=jn=[];Cc=0,ja=Or(),_a={status:"pending",value:void 0,then:function(a){l.push(a)}}}return Cc++,t.then(Ko,Ko),t}function Ko(){if(--Cc===0&&jn!==null){_a!==null&&(_a.status="fulfilled");var e=jn;jn=null,ja=0,_a=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function g0(e,t){var l=[],a={status:"pending",value:null,reason:null,then:function(n){l.push(n)}};return e.then(function(){a.status="fulfilled",a.value=t;for(var n=0;n<l.length;n++)(0,l[n])(t)},function(n){for(a.status="rejected",a.reason=n,n=0;n<l.length;n++)(0,l[n])(void 0)}),a}var Jo=D.S;D.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&v0(e,t),Jo!==null&&Jo(e,t)};var ea=Y(null);function Dc(){var e=ea.current;return e!==null?e:Ce.pooledCache}function zi(e,t){t===null?Q(ea,ea.current):Q(ea,t.pool)}function $o(){var e=Dc();return e===null?null:{parent:Xe._currentValue,pool:e}}var _n=Error(c(460)),Wo=Error(c(474)),Ui=Error(c(542)),Oc={then:function(){}};function Fo(e){return e=e.status,e==="fulfilled"||e==="rejected"}function Hi(){}function Po(e,t,l){switch(l=e[l],l===void 0?e.push(t):l!==t&&(t.then(Hi,Hi),t=l),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,ef(e),e;default:if(typeof t.status=="string")t.then(Hi,Hi);else{if(e=Ce,e!==null&&100<e.shellSuspendCounter)throw Error(c(482));e=t,e.status="pending",e.then(function(a){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=a}},function(a){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=a}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,ef(e),e}throw Mn=t,_n}}var Mn=null;function Io(){if(Mn===null)throw Error(c(459));var e=Mn;return Mn=null,e}function ef(e){if(e===_n||e===Ui)throw Error(c(483))}var pl=!1;function zc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Uc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function bl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function xl(e,t,l){var a=e.updateQueue;if(a===null)return null;if(a=a.shared,(Te&2)!==0){var n=a.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),a.pending=t,t=Mi(e),qo(e,null,l),t}return _i(e,a,t,l),Mi(e)}function Rn(e,t,l){if(t=t.updateQueue,t!==null&&(t=t.shared,(l&4194048)!==0)){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Zs(e,l)}}function Hc(e,t){var l=e.updateQueue,a=e.alternate;if(a!==null&&(a=a.updateQueue,l===a)){var n=null,u=null;if(l=l.firstBaseUpdate,l!==null){do{var o={lane:l.lane,tag:l.tag,payload:l.payload,callback:null,next:null};u===null?n=u=o:u=u.next=o,l=l.next}while(l!==null);u===null?n=u=t:u=u.next=t}else n=u=t;l={baseState:a.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:a.shared,callbacks:a.callbacks},e.updateQueue=l;return}e=l.lastBaseUpdate,e===null?l.firstBaseUpdate=t:e.next=t,l.lastBaseUpdate=t}var kc=!1;function wn(){if(kc){var e=_a;if(e!==null)throw e}}function Cn(e,t,l,a){kc=!1;var n=e.updateQueue;pl=!1;var u=n.firstBaseUpdate,o=n.lastBaseUpdate,m=n.shared.pending;if(m!==null){n.shared.pending=null;var p=m,M=p.next;p.next=null,o===null?u=M:o.next=M,o=p;var k=e.alternate;k!==null&&(k=k.updateQueue,m=k.lastBaseUpdate,m!==o&&(m===null?k.firstBaseUpdate=M:m.next=M,k.lastBaseUpdate=p))}if(u!==null){var q=n.baseState;o=0,k=M=p=null,m=u;do{var w=m.lane&-536870913,C=w!==m.lane;if(C?(ye&w)===w:(a&w)===w){w!==0&&w===ja&&(kc=!0),k!==null&&(k=k.next={lane:0,tag:m.tag,payload:m.payload,callback:null,next:null});e:{var ue=e,ae=m;w=t;var Me=l;switch(ae.tag){case 1:if(ue=ae.payload,typeof ue=="function"){q=ue.call(Me,q,w);break e}q=ue;break e;case 3:ue.flags=ue.flags&-65537|128;case 0:if(ue=ae.payload,w=typeof ue=="function"?ue.call(Me,q,w):ue,w==null)break e;q=E({},q,w);break e;case 2:pl=!0}}w=m.callback,w!==null&&(e.flags|=64,C&&(e.flags|=8192),C=n.callbacks,C===null?n.callbacks=[w]:C.push(w))}else C={lane:w,tag:m.tag,payload:m.payload,callback:m.callback,next:null},k===null?(M=k=C,p=q):k=k.next=C,o|=w;if(m=m.next,m===null){if(m=n.shared.pending,m===null)break;C=m,m=C.next,C.next=null,n.lastBaseUpdate=C,n.shared.pending=null}}while(!0);k===null&&(p=q),n.baseState=p,n.firstBaseUpdate=M,n.lastBaseUpdate=k,u===null&&(n.shared.lanes=0),Ml|=o,e.lanes=o,e.memoizedState=q}}function tf(e,t){if(typeof e!="function")throw Error(c(191,e));e.call(t)}function lf(e,t){var l=e.callbacks;if(l!==null)for(e.callbacks=null,e=0;e<l.length;e++)tf(l[e],t)}var Ma=Y(null),ki=Y(0);function af(e,t){e=cl,Q(ki,e),Q(Ma,t),cl=e|t.baseLanes}function Bc(){Q(ki,cl),Q(Ma,Ma.current)}function Lc(){cl=ki.current,$(Ma),$(ki)}var Sl=0,fe=null,je=null,Ge=null,Bi=!1,Ra=!1,ta=!1,Li=0,Dn=0,wa=null,y0=0;function Le(){throw Error(c(321))}function qc(e,t){if(t===null)return!1;for(var l=0;l<t.length&&l<e.length;l++)if(!yt(e[l],t[l]))return!1;return!0}function Gc(e,t,l,a,n,u){return Sl=u,fe=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,D.H=e===null||e.memoizedState===null?Gf:Yf,ta=!1,u=l(a,n),ta=!1,Ra&&(u=uf(t,l,a,n)),nf(e),u}function nf(e){D.H=Qi;var t=je!==null&&je.next!==null;if(Sl=0,Ge=je=fe=null,Bi=!1,Dn=0,wa=null,t)throw Error(c(300));e===null||We||(e=e.dependencies,e!==null&&Di(e)&&(We=!0))}function uf(e,t,l,a){fe=e;var n=0;do{if(Ra&&(wa=null),Dn=0,Ra=!1,25<=n)throw Error(c(301));if(n+=1,Ge=je=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}D.H=T0,u=t(l,a)}while(Ra);return u}function p0(){var e=D.H,t=e.useState()[0];return t=typeof t.then=="function"?On(t):t,e=e.useState()[0],(je!==null?je.memoizedState:null)!==e&&(fe.flags|=1024),t}function Yc(){var e=Li!==0;return Li=0,e}function Vc(e,t,l){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~l}function Xc(e){if(Bi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Bi=!1}Sl=0,Ge=je=fe=null,Ra=!1,Dn=Li=0,wa=null}function ft(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ge===null?fe.memoizedState=Ge=e:Ge=Ge.next=e,Ge}function Ye(){if(je===null){var e=fe.alternate;e=e!==null?e.memoizedState:null}else e=je.next;var t=Ge===null?fe.memoizedState:Ge.next;if(t!==null)Ge=t,je=e;else{if(e===null)throw fe.alternate===null?Error(c(467)):Error(c(310));je=e,e={memoizedState:je.memoizedState,baseState:je.baseState,baseQueue:je.baseQueue,queue:je.queue,next:null},Ge===null?fe.memoizedState=Ge=e:Ge=Ge.next=e}return Ge}function Qc(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function On(e){var t=Dn;return Dn+=1,wa===null&&(wa=[]),e=Po(wa,e,t),t=fe,(Ge===null?t.memoizedState:Ge.next)===null&&(t=t.alternate,D.H=t===null||t.memoizedState===null?Gf:Yf),e}function qi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return On(e);if(e.$$typeof===U)return nt(e)}throw Error(c(438,String(e)))}function Zc(e){var t=null,l=fe.updateQueue;if(l!==null&&(t=l.memoCache),t==null){var a=fe.alternate;a!==null&&(a=a.updateQueue,a!==null&&(a=a.memoCache,a!=null&&(t={data:a.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),l===null&&(l=Qc(),fe.updateQueue=l),l.memoCache=t,l=t.data[t.index],l===void 0)for(l=t.data[t.index]=Array(e),a=0;a<e;a++)l[a]=re;return t.index++,l}function tl(e,t){return typeof t=="function"?t(e):t}function Gi(e){var t=Ye();return Kc(t,je,e)}function Kc(e,t,l){var a=e.queue;if(a===null)throw Error(c(311));a.lastRenderedReducer=l;var n=e.baseQueue,u=a.pending;if(u!==null){if(n!==null){var o=n.next;n.next=u.next,u.next=o}t.baseQueue=n=u,a.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{t=n.next;var m=o=null,p=null,M=t,k=!1;do{var q=M.lane&-536870913;if(q!==M.lane?(ye&q)===q:(Sl&q)===q){var w=M.revertLane;if(w===0)p!==null&&(p=p.next={lane:0,revertLane:0,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null}),q===ja&&(k=!0);else if((Sl&w)===w){M=M.next,w===ja&&(k=!0);continue}else q={lane:0,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},p===null?(m=p=q,o=u):p=p.next=q,fe.lanes|=w,Ml|=w;q=M.action,ta&&l(u,q),u=M.hasEagerState?M.eagerState:l(u,q)}else w={lane:q,revertLane:M.revertLane,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null},p===null?(m=p=w,o=u):p=p.next=w,fe.lanes|=q,Ml|=q;M=M.next}while(M!==null&&M!==t);if(p===null?o=u:p.next=m,!yt(u,e.memoizedState)&&(We=!0,k&&(l=_a,l!==null)))throw l;e.memoizedState=u,e.baseState=o,e.baseQueue=p,a.lastRenderedState=u}return n===null&&(a.lanes=0),[e.memoizedState,a.dispatch]}function Jc(e){var t=Ye(),l=t.queue;if(l===null)throw Error(c(311));l.lastRenderedReducer=e;var a=l.dispatch,n=l.pending,u=t.memoizedState;if(n!==null){l.pending=null;var o=n=n.next;do u=e(u,o.action),o=o.next;while(o!==n);yt(u,t.memoizedState)||(We=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),l.lastRenderedState=u}return[u,a]}function cf(e,t,l){var a=fe,n=Ye(),u=Ee;if(u){if(l===void 0)throw Error(c(407));l=l()}else l=t();var o=!yt((je||n).memoizedState,l);o&&(n.memoizedState=l,We=!0),n=n.queue;var m=of.bind(null,a,n,e);if(zn(2048,8,m,[e]),n.getSnapshot!==t||o||Ge!==null&&Ge.memoizedState.tag&1){if(a.flags|=2048,Ca(9,Yi(),sf.bind(null,a,n,l,t),null),Ce===null)throw Error(c(349));u||(Sl&124)!==0||rf(a,t,l)}return l}function rf(e,t,l){e.flags|=16384,e={getSnapshot:t,value:l},t=fe.updateQueue,t===null?(t=Qc(),fe.updateQueue=t,t.stores=[e]):(l=t.stores,l===null?t.stores=[e]:l.push(e))}function sf(e,t,l,a){t.value=l,t.getSnapshot=a,ff(t)&&df(e)}function of(e,t,l){return l(function(){ff(t)&&df(e)})}function ff(e){var t=e.getSnapshot;e=e.value;try{var l=t();return!yt(e,l)}catch{return!0}}function df(e){var t=Ea(e,2);t!==null&&Nt(t,e,2)}function $c(e){var t=ft();if(typeof e=="function"){var l=e;if(e=l(),ta){hl(!0);try{l()}finally{hl(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:tl,lastRenderedState:e},t}function mf(e,t,l,a){return e.baseState=l,Kc(e,je,typeof a=="function"?a:tl)}function b0(e,t,l,a,n){if(Xi(e))throw Error(c(485));if(e=t.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(o){u.listeners.push(o)}};D.T!==null?l(!0):u.isTransition=!1,a(u),l=t.pending,l===null?(u.next=t.pending=u,hf(t,u)):(u.next=l.next,t.pending=l.next=u)}}function hf(e,t){var l=t.action,a=t.payload,n=e.state;if(t.isTransition){var u=D.T,o={};D.T=o;try{var m=l(n,a),p=D.S;p!==null&&p(o,m),vf(e,t,m)}catch(M){Wc(e,t,M)}finally{D.T=u}}else try{u=l(n,a),vf(e,t,u)}catch(M){Wc(e,t,M)}}function vf(e,t,l){l!==null&&typeof l=="object"&&typeof l.then=="function"?l.then(function(a){gf(e,t,a)},function(a){return Wc(e,t,a)}):gf(e,t,l)}function gf(e,t,l){t.status="fulfilled",t.value=l,yf(t),e.state=l,t=e.pending,t!==null&&(l=t.next,l===t?e.pending=null:(l=l.next,t.next=l,hf(e,l)))}function Wc(e,t,l){var a=e.pending;if(e.pending=null,a!==null){a=a.next;do t.status="rejected",t.reason=l,yf(t),t=t.next;while(t!==a)}e.action=null}function yf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function pf(e,t){return t}function bf(e,t){if(Ee){var l=Ce.formState;if(l!==null){e:{var a=fe;if(Ee){if(ke){t:{for(var n=ke,u=Gt;n.nodeType!==8;){if(!u){n=null;break t}if(n=Ht(n.nextSibling),n===null){n=null;break t}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){ke=Ht(n.nextSibling),a=n.data==="F!";break e}}Fl(a)}a=!1}a&&(t=l[0])}}return l=ft(),l.memoizedState=l.baseState=t,a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:pf,lastRenderedState:t},l.queue=a,l=Bf.bind(null,fe,a),a.dispatch=l,a=$c(!1),u=tr.bind(null,fe,!1,a.queue),a=ft(),n={state:t,dispatch:null,action:e,pending:null},a.queue=n,l=b0.bind(null,fe,n,u,l),n.dispatch=l,a.memoizedState=e,[t,l,!1]}function xf(e){var t=Ye();return Sf(t,je,e)}function Sf(e,t,l){if(t=Kc(e,t,pf)[0],e=Gi(tl)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var a=On(t)}catch(o){throw o===_n?Ui:o}else a=t;t=Ye();var n=t.queue,u=n.dispatch;return l!==t.memoizedState&&(fe.flags|=2048,Ca(9,Yi(),x0.bind(null,n,l),null)),[a,u,e]}function x0(e,t){e.action=t}function Ef(e){var t=Ye(),l=je;if(l!==null)return Sf(t,l,e);Ye(),t=t.memoizedState,l=Ye();var a=l.queue.dispatch;return l.memoizedState=e,[t,a,!1]}function Ca(e,t,l,a){return e={tag:e,create:l,deps:a,inst:t,next:null},t=fe.updateQueue,t===null&&(t=Qc(),fe.updateQueue=t),l=t.lastEffect,l===null?t.lastEffect=e.next=e:(a=l.next,l.next=e,e.next=a,t.lastEffect=e),e}function Yi(){return{destroy:void 0,resource:void 0}}function Nf(){return Ye().memoizedState}function Vi(e,t,l,a){var n=ft();a=a===void 0?null:a,fe.flags|=e,n.memoizedState=Ca(1|t,Yi(),l,a)}function zn(e,t,l,a){var n=Ye();a=a===void 0?null:a;var u=n.memoizedState.inst;je!==null&&a!==null&&qc(a,je.memoizedState.deps)?n.memoizedState=Ca(t,u,l,a):(fe.flags|=e,n.memoizedState=Ca(1|t,u,l,a))}function Tf(e,t){Vi(8390656,8,e,t)}function Af(e,t){zn(2048,8,e,t)}function jf(e,t){return zn(4,2,e,t)}function _f(e,t){return zn(4,4,e,t)}function Mf(e,t){if(typeof t=="function"){e=e();var l=t(e);return function(){typeof l=="function"?l():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Rf(e,t,l){l=l!=null?l.concat([e]):null,zn(4,4,Mf.bind(null,t,e),l)}function Fc(){}function wf(e,t){var l=Ye();t=t===void 0?null:t;var a=l.memoizedState;return t!==null&&qc(t,a[1])?a[0]:(l.memoizedState=[e,t],e)}function Cf(e,t){var l=Ye();t=t===void 0?null:t;var a=l.memoizedState;if(t!==null&&qc(t,a[1]))return a[0];if(a=e(),ta){hl(!0);try{e()}finally{hl(!1)}}return l.memoizedState=[a,t],a}function Pc(e,t,l){return l===void 0||(Sl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=l,e=zd(),fe.lanes|=e,Ml|=e,l)}function Df(e,t,l,a){return yt(l,t)?l:Ma.current!==null?(e=Pc(e,l,a),yt(e,t)||(We=!0),e):(Sl&42)===0?(We=!0,e.memoizedState=l):(e=zd(),fe.lanes|=e,Ml|=e,t)}function Of(e,t,l,a,n){var u=K.p;K.p=u!==0&&8>u?u:8;var o=D.T,m={};D.T=m,tr(e,!1,t,l);try{var p=n(),M=D.S;if(M!==null&&M(m,p),p!==null&&typeof p=="object"&&typeof p.then=="function"){var k=g0(p,a);Un(e,t,k,Et(e))}else Un(e,t,a,Et(e))}catch(q){Un(e,t,{then:function(){},status:"rejected",reason:q},Et())}finally{K.p=u,D.T=o}}function S0(){}function Ic(e,t,l,a){if(e.tag!==5)throw Error(c(476));var n=zf(e).queue;Of(e,n,t,G,l===null?S0:function(){return Uf(e),l(a)})}function zf(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:G,baseState:G,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:tl,lastRenderedState:G},next:null};var l={};return t.next={memoizedState:l,baseState:l,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:tl,lastRenderedState:l},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function Uf(e){var t=zf(e).next.queue;Un(e,t,{},Et())}function er(){return nt(In)}function Hf(){return Ye().memoizedState}function kf(){return Ye().memoizedState}function E0(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var l=Et();e=bl(l);var a=xl(t,e,l);a!==null&&(Nt(a,t,l),Rn(a,t,l)),t={cache:wc()},e.payload=t;return}t=t.return}}function N0(e,t,l){var a=Et();l={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null},Xi(e)?Lf(t,l):(l=xc(e,t,l,a),l!==null&&(Nt(l,e,a),qf(l,t,a)))}function Bf(e,t,l){var a=Et();Un(e,t,l,a)}function Un(e,t,l,a){var n={lane:a,revertLane:0,action:l,hasEagerState:!1,eagerState:null,next:null};if(Xi(e))Lf(t,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var o=t.lastRenderedState,m=u(o,l);if(n.hasEagerState=!0,n.eagerState=m,yt(m,o))return _i(e,t,n,0),Ce===null&&ji(),!1}catch{}finally{}if(l=xc(e,t,n,a),l!==null)return Nt(l,e,a),qf(l,t,a),!0}return!1}function tr(e,t,l,a){if(a={lane:2,revertLane:Or(),action:a,hasEagerState:!1,eagerState:null,next:null},Xi(e)){if(t)throw Error(c(479))}else t=xc(e,l,a,2),t!==null&&Nt(t,e,2)}function Xi(e){var t=e.alternate;return e===fe||t!==null&&t===fe}function Lf(e,t){Ra=Bi=!0;var l=e.pending;l===null?t.next=t:(t.next=l.next,l.next=t),e.pending=t}function qf(e,t,l){if((l&4194048)!==0){var a=t.lanes;a&=e.pendingLanes,l|=a,t.lanes=l,Zs(e,l)}}var Qi={readContext:nt,use:qi,useCallback:Le,useContext:Le,useEffect:Le,useImperativeHandle:Le,useLayoutEffect:Le,useInsertionEffect:Le,useMemo:Le,useReducer:Le,useRef:Le,useState:Le,useDebugValue:Le,useDeferredValue:Le,useTransition:Le,useSyncExternalStore:Le,useId:Le,useHostTransitionStatus:Le,useFormState:Le,useActionState:Le,useOptimistic:Le,useMemoCache:Le,useCacheRefresh:Le},Gf={readContext:nt,use:qi,useCallback:function(e,t){return ft().memoizedState=[e,t===void 0?null:t],e},useContext:nt,useEffect:Tf,useImperativeHandle:function(e,t,l){l=l!=null?l.concat([e]):null,Vi(4194308,4,Mf.bind(null,t,e),l)},useLayoutEffect:function(e,t){return Vi(4194308,4,e,t)},useInsertionEffect:function(e,t){Vi(4,2,e,t)},useMemo:function(e,t){var l=ft();t=t===void 0?null:t;var a=e();if(ta){hl(!0);try{e()}finally{hl(!1)}}return l.memoizedState=[a,t],a},useReducer:function(e,t,l){var a=ft();if(l!==void 0){var n=l(t);if(ta){hl(!0);try{l(t)}finally{hl(!1)}}}else n=t;return a.memoizedState=a.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},a.queue=e,e=e.dispatch=N0.bind(null,fe,e),[a.memoizedState,e]},useRef:function(e){var t=ft();return e={current:e},t.memoizedState=e},useState:function(e){e=$c(e);var t=e.queue,l=Bf.bind(null,fe,t);return t.dispatch=l,[e.memoizedState,l]},useDebugValue:Fc,useDeferredValue:function(e,t){var l=ft();return Pc(l,e,t)},useTransition:function(){var e=$c(!1);return e=Of.bind(null,fe,e.queue,!0,!1),ft().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,l){var a=fe,n=ft();if(Ee){if(l===void 0)throw Error(c(407));l=l()}else{if(l=t(),Ce===null)throw Error(c(349));(ye&124)!==0||rf(a,t,l)}n.memoizedState=l;var u={value:l,getSnapshot:t};return n.queue=u,Tf(of.bind(null,a,u,e),[e]),a.flags|=2048,Ca(9,Yi(),sf.bind(null,a,u,l,t),null),l},useId:function(){var e=ft(),t=Ce.identifierPrefix;if(Ee){var l=Pt,a=Ft;l=(a&~(1<<32-gt(a)-1)).toString(32)+l,t="«"+t+"R"+l,l=Li++,0<l&&(t+="H"+l.toString(32)),t+="»"}else l=y0++,t="«"+t+"r"+l.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:er,useFormState:bf,useActionState:bf,useOptimistic:function(e){var t=ft();t.memoizedState=t.baseState=e;var l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=l,t=tr.bind(null,fe,!0,l),l.dispatch=t,[e,t]},useMemoCache:Zc,useCacheRefresh:function(){return ft().memoizedState=E0.bind(null,fe)}},Yf={readContext:nt,use:qi,useCallback:wf,useContext:nt,useEffect:Af,useImperativeHandle:Rf,useInsertionEffect:jf,useLayoutEffect:_f,useMemo:Cf,useReducer:Gi,useRef:Nf,useState:function(){return Gi(tl)},useDebugValue:Fc,useDeferredValue:function(e,t){var l=Ye();return Df(l,je.memoizedState,e,t)},useTransition:function(){var e=Gi(tl)[0],t=Ye().memoizedState;return[typeof e=="boolean"?e:On(e),t]},useSyncExternalStore:cf,useId:Hf,useHostTransitionStatus:er,useFormState:xf,useActionState:xf,useOptimistic:function(e,t){var l=Ye();return mf(l,je,e,t)},useMemoCache:Zc,useCacheRefresh:kf},T0={readContext:nt,use:qi,useCallback:wf,useContext:nt,useEffect:Af,useImperativeHandle:Rf,useInsertionEffect:jf,useLayoutEffect:_f,useMemo:Cf,useReducer:Jc,useRef:Nf,useState:function(){return Jc(tl)},useDebugValue:Fc,useDeferredValue:function(e,t){var l=Ye();return je===null?Pc(l,e,t):Df(l,je.memoizedState,e,t)},useTransition:function(){var e=Jc(tl)[0],t=Ye().memoizedState;return[typeof e=="boolean"?e:On(e),t]},useSyncExternalStore:cf,useId:Hf,useHostTransitionStatus:er,useFormState:Ef,useActionState:Ef,useOptimistic:function(e,t){var l=Ye();return je!==null?mf(l,je,e,t):(l.baseState=e,[e,l.queue.dispatch])},useMemoCache:Zc,useCacheRefresh:kf},Da=null,Hn=0;function Zi(e){var t=Hn;return Hn+=1,Da===null&&(Da=[]),Po(Da,e,t)}function kn(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Ki(e,t){throw t.$$typeof===T?Error(c(525)):(e=Object.prototype.toString.call(t),Error(c(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Vf(e){var t=e._init;return t(e._payload)}function Xf(e){function t(A,N){if(e){var _=A.deletions;_===null?(A.deletions=[N],A.flags|=16):_.push(N)}}function l(A,N){if(!e)return null;for(;N!==null;)t(A,N),N=N.sibling;return null}function a(A){for(var N=new Map;A!==null;)A.key!==null?N.set(A.key,A):N.set(A.index,A),A=A.sibling;return N}function n(A,N){return A=Wt(A,N),A.index=0,A.sibling=null,A}function u(A,N,_){return A.index=_,e?(_=A.alternate,_!==null?(_=_.index,_<N?(A.flags|=67108866,N):_):(A.flags|=67108866,N)):(A.flags|=1048576,N)}function o(A){return e&&A.alternate===null&&(A.flags|=67108866),A}function m(A,N,_,B){return N===null||N.tag!==6?(N=Ec(_,A.mode,B),N.return=A,N):(N=n(N,_),N.return=A,N)}function p(A,N,_,B){var P=_.type;return P===H?k(A,N,_.props.children,B,_.key):N!==null&&(N.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===L&&Vf(P)===N.type)?(N=n(N,_.props),kn(N,_),N.return=A,N):(N=Ri(_.type,_.key,_.props,null,A.mode,B),kn(N,_),N.return=A,N)}function M(A,N,_,B){return N===null||N.tag!==4||N.stateNode.containerInfo!==_.containerInfo||N.stateNode.implementation!==_.implementation?(N=Nc(_,A.mode,B),N.return=A,N):(N=n(N,_.children||[]),N.return=A,N)}function k(A,N,_,B,P){return N===null||N.tag!==7?(N=Kl(_,A.mode,B,P),N.return=A,N):(N=n(N,_),N.return=A,N)}function q(A,N,_){if(typeof N=="string"&&N!==""||typeof N=="number"||typeof N=="bigint")return N=Ec(""+N,A.mode,_),N.return=A,N;if(typeof N=="object"&&N!==null){switch(N.$$typeof){case R:return _=Ri(N.type,N.key,N.props,null,A.mode,_),kn(_,N),_.return=A,_;case O:return N=Nc(N,A.mode,_),N.return=A,N;case L:var B=N._init;return N=B(N._payload),q(A,N,_)}if(Ne(N)||pe(N))return N=Kl(N,A.mode,_,null),N.return=A,N;if(typeof N.then=="function")return q(A,Zi(N),_);if(N.$$typeof===U)return q(A,Oi(A,N),_);Ki(A,N)}return null}function w(A,N,_,B){var P=N!==null?N.key:null;if(typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint")return P!==null?null:m(A,N,""+_,B);if(typeof _=="object"&&_!==null){switch(_.$$typeof){case R:return _.key===P?p(A,N,_,B):null;case O:return _.key===P?M(A,N,_,B):null;case L:return P=_._init,_=P(_._payload),w(A,N,_,B)}if(Ne(_)||pe(_))return P!==null?null:k(A,N,_,B,null);if(typeof _.then=="function")return w(A,N,Zi(_),B);if(_.$$typeof===U)return w(A,N,Oi(A,_),B);Ki(A,_)}return null}function C(A,N,_,B,P){if(typeof B=="string"&&B!==""||typeof B=="number"||typeof B=="bigint")return A=A.get(_)||null,m(N,A,""+B,P);if(typeof B=="object"&&B!==null){switch(B.$$typeof){case R:return A=A.get(B.key===null?_:B.key)||null,p(N,A,B,P);case O:return A=A.get(B.key===null?_:B.key)||null,M(N,A,B,P);case L:var me=B._init;return B=me(B._payload),C(A,N,_,B,P)}if(Ne(B)||pe(B))return A=A.get(_)||null,k(N,A,B,P,null);if(typeof B.then=="function")return C(A,N,_,Zi(B),P);if(B.$$typeof===U)return C(A,N,_,Oi(N,B),P);Ki(N,B)}return null}function ue(A,N,_,B){for(var P=null,me=null,te=N,ie=N=0,Pe=null;te!==null&&ie<_.length;ie++){te.index>ie?(Pe=te,te=null):Pe=te.sibling;var Se=w(A,te,_[ie],B);if(Se===null){te===null&&(te=Pe);break}e&&te&&Se.alternate===null&&t(A,te),N=u(Se,N,ie),me===null?P=Se:me.sibling=Se,me=Se,te=Pe}if(ie===_.length)return l(A,te),Ee&&$l(A,ie),P;if(te===null){for(;ie<_.length;ie++)te=q(A,_[ie],B),te!==null&&(N=u(te,N,ie),me===null?P=te:me.sibling=te,me=te);return Ee&&$l(A,ie),P}for(te=a(te);ie<_.length;ie++)Pe=C(te,A,ie,_[ie],B),Pe!==null&&(e&&Pe.alternate!==null&&te.delete(Pe.key===null?ie:Pe.key),N=u(Pe,N,ie),me===null?P=Pe:me.sibling=Pe,me=Pe);return e&&te.forEach(function(kl){return t(A,kl)}),Ee&&$l(A,ie),P}function ae(A,N,_,B){if(_==null)throw Error(c(151));for(var P=null,me=null,te=N,ie=N=0,Pe=null,Se=_.next();te!==null&&!Se.done;ie++,Se=_.next()){te.index>ie?(Pe=te,te=null):Pe=te.sibling;var kl=w(A,te,Se.value,B);if(kl===null){te===null&&(te=Pe);break}e&&te&&kl.alternate===null&&t(A,te),N=u(kl,N,ie),me===null?P=kl:me.sibling=kl,me=kl,te=Pe}if(Se.done)return l(A,te),Ee&&$l(A,ie),P;if(te===null){for(;!Se.done;ie++,Se=_.next())Se=q(A,Se.value,B),Se!==null&&(N=u(Se,N,ie),me===null?P=Se:me.sibling=Se,me=Se);return Ee&&$l(A,ie),P}for(te=a(te);!Se.done;ie++,Se=_.next())Se=C(te,A,ie,Se.value,B),Se!==null&&(e&&Se.alternate!==null&&te.delete(Se.key===null?ie:Se.key),N=u(Se,N,ie),me===null?P=Se:me.sibling=Se,me=Se);return e&&te.forEach(function(Ay){return t(A,Ay)}),Ee&&$l(A,ie),P}function Me(A,N,_,B){if(typeof _=="object"&&_!==null&&_.type===H&&_.key===null&&(_=_.props.children),typeof _=="object"&&_!==null){switch(_.$$typeof){case R:e:{for(var P=_.key;N!==null;){if(N.key===P){if(P=_.type,P===H){if(N.tag===7){l(A,N.sibling),B=n(N,_.props.children),B.return=A,A=B;break e}}else if(N.elementType===P||typeof P=="object"&&P!==null&&P.$$typeof===L&&Vf(P)===N.type){l(A,N.sibling),B=n(N,_.props),kn(B,_),B.return=A,A=B;break e}l(A,N);break}else t(A,N);N=N.sibling}_.type===H?(B=Kl(_.props.children,A.mode,B,_.key),B.return=A,A=B):(B=Ri(_.type,_.key,_.props,null,A.mode,B),kn(B,_),B.return=A,A=B)}return o(A);case O:e:{for(P=_.key;N!==null;){if(N.key===P)if(N.tag===4&&N.stateNode.containerInfo===_.containerInfo&&N.stateNode.implementation===_.implementation){l(A,N.sibling),B=n(N,_.children||[]),B.return=A,A=B;break e}else{l(A,N);break}else t(A,N);N=N.sibling}B=Nc(_,A.mode,B),B.return=A,A=B}return o(A);case L:return P=_._init,_=P(_._payload),Me(A,N,_,B)}if(Ne(_))return ue(A,N,_,B);if(pe(_)){if(P=pe(_),typeof P!="function")throw Error(c(150));return _=P.call(_),ae(A,N,_,B)}if(typeof _.then=="function")return Me(A,N,Zi(_),B);if(_.$$typeof===U)return Me(A,N,Oi(A,_),B);Ki(A,_)}return typeof _=="string"&&_!==""||typeof _=="number"||typeof _=="bigint"?(_=""+_,N!==null&&N.tag===6?(l(A,N.sibling),B=n(N,_),B.return=A,A=B):(l(A,N),B=Ec(_,A.mode,B),B.return=A,A=B),o(A)):l(A,N)}return function(A,N,_,B){try{Hn=0;var P=Me(A,N,_,B);return Da=null,P}catch(te){if(te===_n||te===Ui)throw te;var me=pt(29,te,null,A.mode);return me.lanes=B,me.return=A,me}finally{}}}var Oa=Xf(!0),Qf=Xf(!1),Ct=Y(null),Yt=null;function El(e){var t=e.alternate;Q(Qe,Qe.current&1),Q(Ct,e),Yt===null&&(t===null||Ma.current!==null||t.memoizedState!==null)&&(Yt=e)}function Zf(e){if(e.tag===22){if(Q(Qe,Qe.current),Q(Ct,e),Yt===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Yt=e)}}else Nl()}function Nl(){Q(Qe,Qe.current),Q(Ct,Ct.current)}function ll(e){$(Ct),Yt===e&&(Yt=null),$(Qe)}var Qe=Y(0);function Ji(e){for(var t=e;t!==null;){if(t.tag===13){var l=t.memoizedState;if(l!==null&&(l=l.dehydrated,l===null||l.data==="$?"||Qr(l)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function lr(e,t,l,a){t=e.memoizedState,l=l(a,t),l=l==null?t:E({},t,l),e.memoizedState=l,e.lanes===0&&(e.updateQueue.baseState=l)}var ar={enqueueSetState:function(e,t,l){e=e._reactInternals;var a=Et(),n=bl(a);n.payload=t,l!=null&&(n.callback=l),t=xl(e,n,a),t!==null&&(Nt(t,e,a),Rn(t,e,a))},enqueueReplaceState:function(e,t,l){e=e._reactInternals;var a=Et(),n=bl(a);n.tag=1,n.payload=t,l!=null&&(n.callback=l),t=xl(e,n,a),t!==null&&(Nt(t,e,a),Rn(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var l=Et(),a=bl(l);a.tag=2,t!=null&&(a.callback=t),t=xl(e,a,l),t!==null&&(Nt(t,e,l),Rn(t,e,l))}};function Kf(e,t,l,a,n,u,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(a,u,o):t.prototype&&t.prototype.isPureReactComponent?!bn(l,a)||!bn(n,u):!0}function Jf(e,t,l,a){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(l,a),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(l,a),t.state!==e&&ar.enqueueReplaceState(t,t.state,null)}function la(e,t){var l=t;if("ref"in t){l={};for(var a in t)a!=="ref"&&(l[a]=t[a])}if(e=e.defaultProps){l===t&&(l=E({},l));for(var n in e)l[n]===void 0&&(l[n]=e[n])}return l}var $i=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function $f(e){$i(e)}function Wf(e){console.error(e)}function Ff(e){$i(e)}function Wi(e,t){try{var l=e.onUncaughtError;l(t.value,{componentStack:t.stack})}catch(a){setTimeout(function(){throw a})}}function Pf(e,t,l){try{var a=e.onCaughtError;a(l.value,{componentStack:l.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function nr(e,t,l){return l=bl(l),l.tag=3,l.payload={element:null},l.callback=function(){Wi(e,t)},l}function If(e){return e=bl(e),e.tag=3,e}function ed(e,t,l,a){var n=l.type.getDerivedStateFromError;if(typeof n=="function"){var u=a.value;e.payload=function(){return n(u)},e.callback=function(){Pf(t,l,a)}}var o=l.stateNode;o!==null&&typeof o.componentDidCatch=="function"&&(e.callback=function(){Pf(t,l,a),typeof n!="function"&&(Rl===null?Rl=new Set([this]):Rl.add(this));var m=a.stack;this.componentDidCatch(a.value,{componentStack:m!==null?m:""})})}function A0(e,t,l,a,n){if(l.flags|=32768,a!==null&&typeof a=="object"&&typeof a.then=="function"){if(t=l.alternate,t!==null&&Tn(t,l,n,!0),l=Ct.current,l!==null){switch(l.tag){case 13:return Yt===null?Mr():l.alternate===null&&Be===0&&(Be=3),l.flags&=-257,l.flags|=65536,l.lanes=n,a===Oc?l.flags|=16384:(t=l.updateQueue,t===null?l.updateQueue=new Set([a]):t.add(a),wr(e,a,n)),!1;case 22:return l.flags|=65536,a===Oc?l.flags|=16384:(t=l.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([a])},l.updateQueue=t):(l=t.retryQueue,l===null?t.retryQueue=new Set([a]):l.add(a)),wr(e,a,n)),!1}throw Error(c(435,l.tag))}return wr(e,a,n),Mr(),!1}if(Ee)return t=Ct.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,a!==jc&&(e=Error(c(422),{cause:a}),Nn(_t(e,l)))):(a!==jc&&(t=Error(c(423),{cause:a}),Nn(_t(t,l))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,a=_t(a,l),n=nr(e.stateNode,a,n),Hc(e,n),Be!==4&&(Be=2)),!1;var u=Error(c(520),{cause:a});if(u=_t(u,l),Xn===null?Xn=[u]:Xn.push(u),Be!==4&&(Be=2),t===null)return!0;a=_t(a,l),l=t;do{switch(l.tag){case 3:return l.flags|=65536,e=n&-n,l.lanes|=e,e=nr(l.stateNode,a,e),Hc(l,e),!1;case 1:if(t=l.type,u=l.stateNode,(l.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Rl===null||!Rl.has(u))))return l.flags|=65536,n&=-n,l.lanes|=n,n=If(n),ed(n,e,l,a),Hc(l,n),!1}l=l.return}while(l!==null);return!1}var td=Error(c(461)),We=!1;function Ie(e,t,l,a){t.child=e===null?Qf(t,null,l,a):Oa(t,e.child,l,a)}function ld(e,t,l,a,n){l=l.render;var u=t.ref;if("ref"in a){var o={};for(var m in a)m!=="ref"&&(o[m]=a[m])}else o=a;return Il(t),a=Gc(e,t,l,o,u,n),m=Yc(),e!==null&&!We?(Vc(e,t,n),al(e,t,n)):(Ee&&m&&Tc(t),t.flags|=1,Ie(e,t,a,n),t.child)}function ad(e,t,l,a,n){if(e===null){var u=l.type;return typeof u=="function"&&!Sc(u)&&u.defaultProps===void 0&&l.compare===null?(t.tag=15,t.type=u,nd(e,t,u,a,n)):(e=Ri(l.type,null,a,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!dr(e,n)){var o=u.memoizedProps;if(l=l.compare,l=l!==null?l:bn,l(o,a)&&e.ref===t.ref)return al(e,t,n)}return t.flags|=1,e=Wt(u,a),e.ref=t.ref,e.return=t,t.child=e}function nd(e,t,l,a,n){if(e!==null){var u=e.memoizedProps;if(bn(u,a)&&e.ref===t.ref)if(We=!1,t.pendingProps=a=u,dr(e,n))(e.flags&131072)!==0&&(We=!0);else return t.lanes=e.lanes,al(e,t,n)}return ir(e,t,l,a,n)}function id(e,t,l){var a=t.pendingProps,n=a.children,u=e!==null?e.memoizedState:null;if(a.mode==="hidden"){if((t.flags&128)!==0){if(a=u!==null?u.baseLanes|l:l,e!==null){for(n=t.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;t.childLanes=u&~a}else t.childLanes=0,t.child=null;return ud(e,t,a,l)}if((l&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&zi(t,u!==null?u.cachePool:null),u!==null?af(t,u):Bc(),Zf(t);else return t.lanes=t.childLanes=536870912,ud(e,t,u!==null?u.baseLanes|l:l,l)}else u!==null?(zi(t,u.cachePool),af(t,u),Nl(),t.memoizedState=null):(e!==null&&zi(t,null),Bc(),Nl());return Ie(e,t,n,l),t.child}function ud(e,t,l,a){var n=Dc();return n=n===null?null:{parent:Xe._currentValue,pool:n},t.memoizedState={baseLanes:l,cachePool:n},e!==null&&zi(t,null),Bc(),Zf(t),e!==null&&Tn(e,t,a,!0),null}function Fi(e,t){var l=t.ref;if(l===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof l!="function"&&typeof l!="object")throw Error(c(284));(e===null||e.ref!==l)&&(t.flags|=4194816)}}function ir(e,t,l,a,n){return Il(t),l=Gc(e,t,l,a,void 0,n),a=Yc(),e!==null&&!We?(Vc(e,t,n),al(e,t,n)):(Ee&&a&&Tc(t),t.flags|=1,Ie(e,t,l,n),t.child)}function cd(e,t,l,a,n,u){return Il(t),t.updateQueue=null,l=uf(t,a,l,n),nf(e),a=Yc(),e!==null&&!We?(Vc(e,t,u),al(e,t,u)):(Ee&&a&&Tc(t),t.flags|=1,Ie(e,t,l,u),t.child)}function rd(e,t,l,a,n){if(Il(t),t.stateNode===null){var u=Na,o=l.contextType;typeof o=="object"&&o!==null&&(u=nt(o)),u=new l(a,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=ar,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=a,u.state=t.memoizedState,u.refs={},zc(t),o=l.contextType,u.context=typeof o=="object"&&o!==null?nt(o):Na,u.state=t.memoizedState,o=l.getDerivedStateFromProps,typeof o=="function"&&(lr(t,l,o,a),u.state=t.memoizedState),typeof l.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(o=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),o!==u.state&&ar.enqueueReplaceState(u,u.state,null),Cn(t,a,u,n),wn(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!0}else if(e===null){u=t.stateNode;var m=t.memoizedProps,p=la(l,m);u.props=p;var M=u.context,k=l.contextType;o=Na,typeof k=="object"&&k!==null&&(o=nt(k));var q=l.getDerivedStateFromProps;k=typeof q=="function"||typeof u.getSnapshotBeforeUpdate=="function",m=t.pendingProps!==m,k||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(m||M!==o)&&Jf(t,u,a,o),pl=!1;var w=t.memoizedState;u.state=w,Cn(t,a,u,n),wn(),M=t.memoizedState,m||w!==M||pl?(typeof q=="function"&&(lr(t,l,q,a),M=t.memoizedState),(p=pl||Kf(t,l,p,a,w,M,o))?(k||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=a,t.memoizedState=M),u.props=a,u.state=M,u.context=o,a=p):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),a=!1)}else{u=t.stateNode,Uc(e,t),o=t.memoizedProps,k=la(l,o),u.props=k,q=t.pendingProps,w=u.context,M=l.contextType,p=Na,typeof M=="object"&&M!==null&&(p=nt(M)),m=l.getDerivedStateFromProps,(M=typeof m=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o!==q||w!==p)&&Jf(t,u,a,p),pl=!1,w=t.memoizedState,u.state=w,Cn(t,a,u,n),wn();var C=t.memoizedState;o!==q||w!==C||pl||e!==null&&e.dependencies!==null&&Di(e.dependencies)?(typeof m=="function"&&(lr(t,l,m,a),C=t.memoizedState),(k=pl||Kf(t,l,k,a,w,C,p)||e!==null&&e.dependencies!==null&&Di(e.dependencies))?(M||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(a,C,p),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(a,C,p)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||o===e.memoizedProps&&w===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&w===e.memoizedState||(t.flags|=1024),t.memoizedProps=a,t.memoizedState=C),u.props=a,u.state=C,u.context=p,a=k):(typeof u.componentDidUpdate!="function"||o===e.memoizedProps&&w===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||o===e.memoizedProps&&w===e.memoizedState||(t.flags|=1024),a=!1)}return u=a,Fi(e,t),a=(t.flags&128)!==0,u||a?(u=t.stateNode,l=a&&typeof l.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&a?(t.child=Oa(t,e.child,null,n),t.child=Oa(t,null,l,n)):Ie(e,t,l,n),t.memoizedState=u.state,e=t.child):e=al(e,t,n),e}function sd(e,t,l,a){return En(),t.flags|=256,Ie(e,t,l,a),t.child}var ur={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function cr(e){return{baseLanes:e,cachePool:$o()}}function rr(e,t,l){return e=e!==null?e.childLanes&~l:0,t&&(e|=Dt),e}function od(e,t,l){var a=t.pendingProps,n=!1,u=(t.flags&128)!==0,o;if((o=u)||(o=e!==null&&e.memoizedState===null?!1:(Qe.current&2)!==0),o&&(n=!0,t.flags&=-129),o=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ee){if(n?El(t):Nl(),Ee){var m=ke,p;if(p=m){e:{for(p=m,m=Gt;p.nodeType!==8;){if(!m){m=null;break e}if(p=Ht(p.nextSibling),p===null){m=null;break e}}m=p}m!==null?(t.memoizedState={dehydrated:m,treeContext:Jl!==null?{id:Ft,overflow:Pt}:null,retryLane:536870912,hydrationErrors:null},p=pt(18,null,null,0),p.stateNode=m,p.return=t,t.child=p,ct=t,ke=null,p=!0):p=!1}p||Fl(t)}if(m=t.memoizedState,m!==null&&(m=m.dehydrated,m!==null))return Qr(m)?t.lanes=32:t.lanes=536870912,null;ll(t)}return m=a.children,a=a.fallback,n?(Nl(),n=t.mode,m=Pi({mode:"hidden",children:m},n),a=Kl(a,n,l,null),m.return=t,a.return=t,m.sibling=a,t.child=m,n=t.child,n.memoizedState=cr(l),n.childLanes=rr(e,o,l),t.memoizedState=ur,a):(El(t),sr(t,m))}if(p=e.memoizedState,p!==null&&(m=p.dehydrated,m!==null)){if(u)t.flags&256?(El(t),t.flags&=-257,t=or(e,t,l)):t.memoizedState!==null?(Nl(),t.child=e.child,t.flags|=128,t=null):(Nl(),n=a.fallback,m=t.mode,a=Pi({mode:"visible",children:a.children},m),n=Kl(n,m,l,null),n.flags|=2,a.return=t,n.return=t,a.sibling=n,t.child=a,Oa(t,e.child,null,l),a=t.child,a.memoizedState=cr(l),a.childLanes=rr(e,o,l),t.memoizedState=ur,t=n);else if(El(t),Qr(m)){if(o=m.nextSibling&&m.nextSibling.dataset,o)var M=o.dgst;o=M,a=Error(c(419)),a.stack="",a.digest=o,Nn({value:a,source:null,stack:null}),t=or(e,t,l)}else if(We||Tn(e,t,l,!1),o=(l&e.childLanes)!==0,We||o){if(o=Ce,o!==null&&(a=l&-l,a=(a&42)!==0?1:Zu(a),a=(a&(o.suspendedLanes|l))!==0?0:a,a!==0&&a!==p.retryLane))throw p.retryLane=a,Ea(e,a),Nt(o,e,a),td;m.data==="$?"||Mr(),t=or(e,t,l)}else m.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=p.treeContext,ke=Ht(m.nextSibling),ct=t,Ee=!0,Wl=null,Gt=!1,e!==null&&(Rt[wt++]=Ft,Rt[wt++]=Pt,Rt[wt++]=Jl,Ft=e.id,Pt=e.overflow,Jl=t),t=sr(t,a.children),t.flags|=4096);return t}return n?(Nl(),n=a.fallback,m=t.mode,p=e.child,M=p.sibling,a=Wt(p,{mode:"hidden",children:a.children}),a.subtreeFlags=p.subtreeFlags&65011712,M!==null?n=Wt(M,n):(n=Kl(n,m,l,null),n.flags|=2),n.return=t,a.return=t,a.sibling=n,t.child=a,a=n,n=t.child,m=e.child.memoizedState,m===null?m=cr(l):(p=m.cachePool,p!==null?(M=Xe._currentValue,p=p.parent!==M?{parent:M,pool:M}:p):p=$o(),m={baseLanes:m.baseLanes|l,cachePool:p}),n.memoizedState=m,n.childLanes=rr(e,o,l),t.memoizedState=ur,a):(El(t),l=e.child,e=l.sibling,l=Wt(l,{mode:"visible",children:a.children}),l.return=t,l.sibling=null,e!==null&&(o=t.deletions,o===null?(t.deletions=[e],t.flags|=16):o.push(e)),t.child=l,t.memoizedState=null,l)}function sr(e,t){return t=Pi({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function Pi(e,t){return e=pt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function or(e,t,l){return Oa(t,e.child,null,l),e=sr(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function fd(e,t,l){e.lanes|=t;var a=e.alternate;a!==null&&(a.lanes|=t),Mc(e.return,t,l)}function fr(e,t,l,a,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:a,tail:l,tailMode:n}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=a,u.tail=l,u.tailMode=n)}function dd(e,t,l){var a=t.pendingProps,n=a.revealOrder,u=a.tail;if(Ie(e,t,a.children,l),a=Qe.current,(a&2)!==0)a=a&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&fd(e,l,t);else if(e.tag===19)fd(e,l,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}a&=1}switch(Q(Qe,a),n){case"forwards":for(l=t.child,n=null;l!==null;)e=l.alternate,e!==null&&Ji(e)===null&&(n=l),l=l.sibling;l=n,l===null?(n=t.child,t.child=null):(n=l.sibling,l.sibling=null),fr(t,!1,n,l,u);break;case"backwards":for(l=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Ji(e)===null){t.child=n;break}e=n.sibling,n.sibling=l,l=n,n=e}fr(t,!0,l,null,u);break;case"together":fr(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function al(e,t,l){if(e!==null&&(t.dependencies=e.dependencies),Ml|=t.lanes,(l&t.childLanes)===0)if(e!==null){if(Tn(e,t,l,!1),(l&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(c(153));if(t.child!==null){for(e=t.child,l=Wt(e,e.pendingProps),t.child=l,l.return=t;e.sibling!==null;)e=e.sibling,l=l.sibling=Wt(e,e.pendingProps),l.return=t;l.sibling=null}return t.child}function dr(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Di(e)))}function j0(e,t,l){switch(t.tag){case 3:Re(t,t.stateNode.containerInfo),yl(t,Xe,e.memoizedState.cache),En();break;case 27:case 5:fl(t);break;case 4:Re(t,t.stateNode.containerInfo);break;case 10:yl(t,t.type,t.memoizedProps.value);break;case 13:var a=t.memoizedState;if(a!==null)return a.dehydrated!==null?(El(t),t.flags|=128,null):(l&t.child.childLanes)!==0?od(e,t,l):(El(t),e=al(e,t,l),e!==null?e.sibling:null);El(t);break;case 19:var n=(e.flags&128)!==0;if(a=(l&t.childLanes)!==0,a||(Tn(e,t,l,!1),a=(l&t.childLanes)!==0),n){if(a)return dd(e,t,l);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),Q(Qe,Qe.current),a)break;return null;case 22:case 23:return t.lanes=0,id(e,t,l);case 24:yl(t,Xe,e.memoizedState.cache)}return al(e,t,l)}function md(e,t,l){if(e!==null)if(e.memoizedProps!==t.pendingProps)We=!0;else{if(!dr(e,l)&&(t.flags&128)===0)return We=!1,j0(e,t,l);We=(e.flags&131072)!==0}else We=!1,Ee&&(t.flags&1048576)!==0&&Yo(t,Ci,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var a=t.elementType,n=a._init;if(a=n(a._payload),t.type=a,typeof a=="function")Sc(a)?(e=la(a,e),t.tag=1,t=rd(null,t,a,e,l)):(t.tag=0,t=ir(null,t,a,e,l));else{if(a!=null){if(n=a.$$typeof,n===X){t.tag=11,t=ld(null,t,a,e,l);break e}else if(n===F){t.tag=14,t=ad(null,t,a,e,l);break e}}throw t=ut(a)||a,Error(c(306,t,""))}}return t;case 0:return ir(e,t,t.type,t.pendingProps,l);case 1:return a=t.type,n=la(a,t.pendingProps),rd(e,t,a,n,l);case 3:e:{if(Re(t,t.stateNode.containerInfo),e===null)throw Error(c(387));a=t.pendingProps;var u=t.memoizedState;n=u.element,Uc(e,t),Cn(t,a,null,l);var o=t.memoizedState;if(a=o.cache,yl(t,Xe,a),a!==u.cache&&Rc(t,[Xe],l,!0),wn(),a=o.element,u.isDehydrated)if(u={element:a,isDehydrated:!1,cache:o.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=sd(e,t,a,l);break e}else if(a!==n){n=_t(Error(c(424)),t),Nn(n),t=sd(e,t,a,l);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(ke=Ht(e.firstChild),ct=t,Ee=!0,Wl=null,Gt=!0,l=Qf(t,null,a,l),t.child=l;l;)l.flags=l.flags&-3|4096,l=l.sibling}else{if(En(),a===n){t=al(e,t,l);break e}Ie(e,t,a,l)}t=t.child}return t;case 26:return Fi(e,t),e===null?(l=ym(t.type,null,t.pendingProps,null))?t.memoizedState=l:Ee||(l=t.type,e=t.pendingProps,a=du(ce.current).createElement(l),a[at]=t,a[st]=e,tt(a,l,e),$e(a),t.stateNode=a):t.memoizedState=ym(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return fl(t),e===null&&Ee&&(a=t.stateNode=hm(t.type,t.pendingProps,ce.current),ct=t,Gt=!0,n=ke,Dl(t.type)?(Zr=n,ke=Ht(a.firstChild)):ke=n),Ie(e,t,t.pendingProps.children,l),Fi(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ee&&((n=a=ke)&&(a=ey(a,t.type,t.pendingProps,Gt),a!==null?(t.stateNode=a,ct=t,ke=Ht(a.firstChild),Gt=!1,n=!0):n=!1),n||Fl(t)),fl(t),n=t.type,u=t.pendingProps,o=e!==null?e.memoizedProps:null,a=u.children,Yr(n,u)?a=null:o!==null&&Yr(n,o)&&(t.flags|=32),t.memoizedState!==null&&(n=Gc(e,t,p0,null,null,l),In._currentValue=n),Fi(e,t),Ie(e,t,a,l),t.child;case 6:return e===null&&Ee&&((e=l=ke)&&(l=ty(l,t.pendingProps,Gt),l!==null?(t.stateNode=l,ct=t,ke=null,e=!0):e=!1),e||Fl(t)),null;case 13:return od(e,t,l);case 4:return Re(t,t.stateNode.containerInfo),a=t.pendingProps,e===null?t.child=Oa(t,null,a,l):Ie(e,t,a,l),t.child;case 11:return ld(e,t,t.type,t.pendingProps,l);case 7:return Ie(e,t,t.pendingProps,l),t.child;case 8:return Ie(e,t,t.pendingProps.children,l),t.child;case 12:return Ie(e,t,t.pendingProps.children,l),t.child;case 10:return a=t.pendingProps,yl(t,t.type,a.value),Ie(e,t,a.children,l),t.child;case 9:return n=t.type._context,a=t.pendingProps.children,Il(t),n=nt(n),a=a(n),t.flags|=1,Ie(e,t,a,l),t.child;case 14:return ad(e,t,t.type,t.pendingProps,l);case 15:return nd(e,t,t.type,t.pendingProps,l);case 19:return dd(e,t,l);case 31:return a=t.pendingProps,l=t.mode,a={mode:a.mode,children:a.children},e===null?(l=Pi(a,l),l.ref=t.ref,t.child=l,l.return=t,t=l):(l=Wt(e.child,a),l.ref=t.ref,t.child=l,l.return=t,t=l),t;case 22:return id(e,t,l);case 24:return Il(t),a=nt(Xe),e===null?(n=Dc(),n===null&&(n=Ce,u=wc(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=l),n=u),t.memoizedState={parent:a,cache:n},zc(t),yl(t,Xe,n)):((e.lanes&l)!==0&&(Uc(e,t),Cn(t,null,null,l),wn()),n=e.memoizedState,u=t.memoizedState,n.parent!==a?(n={parent:a,cache:a},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),yl(t,Xe,a)):(a=u.cache,yl(t,Xe,a),a!==n.cache&&Rc(t,[Xe],l,!0))),Ie(e,t,t.pendingProps.children,l),t.child;case 29:throw t.pendingProps}throw Error(c(156,t.tag))}function nl(e){e.flags|=4}function hd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Em(t)){if(t=Ct.current,t!==null&&((ye&4194048)===ye?Yt!==null:(ye&62914560)!==ye&&(ye&536870912)===0||t!==Yt))throw Mn=Oc,Wo;e.flags|=8192}}function Ii(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Xs():536870912,e.lanes|=t,ka|=t)}function Bn(e,t){if(!Ee)switch(e.tailMode){case"hidden":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?e.tail=null:l.sibling=null;break;case"collapsed":l=e.tail;for(var a=null;l!==null;)l.alternate!==null&&(a=l),l=l.sibling;a===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:a.sibling=null}}function Ue(e){var t=e.alternate!==null&&e.alternate.child===e.child,l=0,a=0;if(t)for(var n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags&65011712,a|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)l|=n.lanes|n.childLanes,a|=n.subtreeFlags,a|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=a,e.childLanes=l,t}function _0(e,t,l){var a=t.pendingProps;switch(Ac(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ue(t),null;case 1:return Ue(t),null;case 3:return l=t.stateNode,a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),el(Xe),ht(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Sn(t)?nl(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Qo())),Ue(t),null;case 26:return l=t.memoizedState,e===null?(nl(t),l!==null?(Ue(t),hd(t,l)):(Ue(t),t.flags&=-16777217)):l?l!==e.memoizedState?(nl(t),Ue(t),hd(t,l)):(Ue(t),t.flags&=-16777217):(e.memoizedProps!==a&&nl(t),Ue(t),t.flags&=-16777217),null;case 27:dl(t),l=ce.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==a&&nl(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return Ue(t),null}e=W.current,Sn(t)?Vo(t):(e=hm(n,a,l),t.stateNode=e,nl(t))}return Ue(t),null;case 5:if(dl(t),l=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==a&&nl(t);else{if(!a){if(t.stateNode===null)throw Error(c(166));return Ue(t),null}if(e=W.current,Sn(t))Vo(t);else{switch(n=du(ce.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;default:switch(l){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",l);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",l);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof a.is=="string"?n.createElement("select",{is:a.is}):n.createElement("select"),a.multiple?e.multiple=!0:a.size&&(e.size=a.size);break;default:e=typeof a.is=="string"?n.createElement(l,{is:a.is}):n.createElement(l)}}e[at]=t,e[st]=a;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(tt(e,l,a),l){case"button":case"input":case"select":case"textarea":e=!!a.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&nl(t)}}return Ue(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==a&&nl(t);else{if(typeof a!="string"&&t.stateNode===null)throw Error(c(166));if(e=ce.current,Sn(t)){if(e=t.stateNode,l=t.memoizedProps,a=null,n=ct,n!==null)switch(n.tag){case 27:case 5:a=n.memoizedProps}e[at]=t,e=!!(e.nodeValue===l||a!==null&&a.suppressHydrationWarning===!0||cm(e.nodeValue,l)),e||Fl(t)}else e=du(e).createTextNode(a),e[at]=t,t.stateNode=e}return Ue(t),null;case 13:if(a=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Sn(t),a!==null&&a.dehydrated!==null){if(e===null){if(!n)throw Error(c(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(c(317));n[at]=t}else En(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Ue(t),n=!1}else n=Qo(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(ll(t),t):(ll(t),null)}if(ll(t),(t.flags&128)!==0)return t.lanes=l,t;if(l=a!==null,e=e!==null&&e.memoizedState!==null,l){a=t.child,n=null,a.alternate!==null&&a.alternate.memoizedState!==null&&a.alternate.memoizedState.cachePool!==null&&(n=a.alternate.memoizedState.cachePool.pool);var u=null;a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(u=a.memoizedState.cachePool.pool),u!==n&&(a.flags|=2048)}return l!==e&&l&&(t.child.flags|=8192),Ii(t,t.updateQueue),Ue(t),null;case 4:return ht(),e===null&&kr(t.stateNode.containerInfo),Ue(t),null;case 10:return el(t.type),Ue(t),null;case 19:if($(Qe),n=t.memoizedState,n===null)return Ue(t),null;if(a=(t.flags&128)!==0,u=n.rendering,u===null)if(a)Bn(n,!1);else{if(Be!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=Ji(e),u!==null){for(t.flags|=128,Bn(n,!1),e=u.updateQueue,t.updateQueue=e,Ii(t,e),t.subtreeFlags=0,e=l,l=t.child;l!==null;)Go(l,e),l=l.sibling;return Q(Qe,Qe.current&1|2),t.child}e=e.sibling}n.tail!==null&&qt()>lu&&(t.flags|=128,a=!0,Bn(n,!1),t.lanes=4194304)}else{if(!a)if(e=Ji(u),e!==null){if(t.flags|=128,a=!0,e=e.updateQueue,t.updateQueue=e,Ii(t,e),Bn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!Ee)return Ue(t),null}else 2*qt()-n.renderingStartTime>lu&&l!==536870912&&(t.flags|=128,a=!0,Bn(n,!1),t.lanes=4194304);n.isBackwards?(u.sibling=t.child,t.child=u):(e=n.last,e!==null?e.sibling=u:t.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=qt(),t.sibling=null,e=Qe.current,Q(Qe,a?e&1|2:e&1),t):(Ue(t),null);case 22:case 23:return ll(t),Lc(),a=t.memoizedState!==null,e!==null?e.memoizedState!==null!==a&&(t.flags|=8192):a&&(t.flags|=8192),a?(l&536870912)!==0&&(t.flags&128)===0&&(Ue(t),t.subtreeFlags&6&&(t.flags|=8192)):Ue(t),l=t.updateQueue,l!==null&&Ii(t,l.retryQueue),l=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),a=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),a!==l&&(t.flags|=2048),e!==null&&$(ea),null;case 24:return l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),el(Xe),Ue(t),null;case 25:return null;case 30:return null}throw Error(c(156,t.tag))}function M0(e,t){switch(Ac(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return el(Xe),ht(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return dl(t),null;case 13:if(ll(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(c(340));En()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return $(Qe),null;case 4:return ht(),null;case 10:return el(t.type),null;case 22:case 23:return ll(t),Lc(),e!==null&&$(ea),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return el(Xe),null;case 25:return null;default:return null}}function vd(e,t){switch(Ac(t),t.tag){case 3:el(Xe),ht();break;case 26:case 27:case 5:dl(t);break;case 4:ht();break;case 13:ll(t);break;case 19:$(Qe);break;case 10:el(t.type);break;case 22:case 23:ll(t),Lc(),e!==null&&$(ea);break;case 24:el(Xe)}}function Ln(e,t){try{var l=t.updateQueue,a=l!==null?l.lastEffect:null;if(a!==null){var n=a.next;l=n;do{if((l.tag&e)===e){a=void 0;var u=l.create,o=l.inst;a=u(),o.destroy=a}l=l.next}while(l!==n)}}catch(m){we(t,t.return,m)}}function Tl(e,t,l){try{var a=t.updateQueue,n=a!==null?a.lastEffect:null;if(n!==null){var u=n.next;a=u;do{if((a.tag&e)===e){var o=a.inst,m=o.destroy;if(m!==void 0){o.destroy=void 0,n=t;var p=l,M=m;try{M()}catch(k){we(n,p,k)}}}a=a.next}while(a!==u)}}catch(k){we(t,t.return,k)}}function gd(e){var t=e.updateQueue;if(t!==null){var l=e.stateNode;try{lf(t,l)}catch(a){we(e,e.return,a)}}}function yd(e,t,l){l.props=la(e.type,e.memoizedProps),l.state=e.memoizedState;try{l.componentWillUnmount()}catch(a){we(e,t,a)}}function qn(e,t){try{var l=e.ref;if(l!==null){switch(e.tag){case 26:case 27:case 5:var a=e.stateNode;break;case 30:a=e.stateNode;break;default:a=e.stateNode}typeof l=="function"?e.refCleanup=l(a):l.current=a}}catch(n){we(e,t,n)}}function Vt(e,t){var l=e.ref,a=e.refCleanup;if(l!==null)if(typeof a=="function")try{a()}catch(n){we(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof l=="function")try{l(null)}catch(n){we(e,t,n)}else l.current=null}function pd(e){var t=e.type,l=e.memoizedProps,a=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":l.autoFocus&&a.focus();break e;case"img":l.src?a.src=l.src:l.srcSet&&(a.srcset=l.srcSet)}}catch(n){we(e,e.return,n)}}function mr(e,t,l){try{var a=e.stateNode;$0(a,e.type,l,t),a[st]=t}catch(n){we(e,e.return,n)}}function bd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Dl(e.type)||e.tag===4}function hr(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||bd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Dl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function vr(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?(l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l).insertBefore(e,t):(t=l.nodeType===9?l.body:l.nodeName==="HTML"?l.ownerDocument.body:l,t.appendChild(e),l=l._reactRootContainer,l!=null||t.onclick!==null||(t.onclick=fu));else if(a!==4&&(a===27&&Dl(e.type)&&(l=e.stateNode,t=null),e=e.child,e!==null))for(vr(e,t,l),e=e.sibling;e!==null;)vr(e,t,l),e=e.sibling}function eu(e,t,l){var a=e.tag;if(a===5||a===6)e=e.stateNode,t?l.insertBefore(e,t):l.appendChild(e);else if(a!==4&&(a===27&&Dl(e.type)&&(l=e.stateNode),e=e.child,e!==null))for(eu(e,t,l),e=e.sibling;e!==null;)eu(e,t,l),e=e.sibling}function xd(e){var t=e.stateNode,l=e.memoizedProps;try{for(var a=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);tt(t,a,l),t[at]=e,t[st]=l}catch(u){we(e,e.return,u)}}var il=!1,qe=!1,gr=!1,Sd=typeof WeakSet=="function"?WeakSet:Set,Fe=null;function R0(e,t){if(e=e.containerInfo,qr=pu,e=Co(e),hc(e)){if("selectionStart"in e)var l={start:e.selectionStart,end:e.selectionEnd};else e:{l=(l=e.ownerDocument)&&l.defaultView||window;var a=l.getSelection&&l.getSelection();if(a&&a.rangeCount!==0){l=a.anchorNode;var n=a.anchorOffset,u=a.focusNode;a=a.focusOffset;try{l.nodeType,u.nodeType}catch{l=null;break e}var o=0,m=-1,p=-1,M=0,k=0,q=e,w=null;t:for(;;){for(var C;q!==l||n!==0&&q.nodeType!==3||(m=o+n),q!==u||a!==0&&q.nodeType!==3||(p=o+a),q.nodeType===3&&(o+=q.nodeValue.length),(C=q.firstChild)!==null;)w=q,q=C;for(;;){if(q===e)break t;if(w===l&&++M===n&&(m=o),w===u&&++k===a&&(p=o),(C=q.nextSibling)!==null)break;q=w,w=q.parentNode}q=C}l=m===-1||p===-1?null:{start:m,end:p}}else l=null}l=l||{start:0,end:0}}else l=null;for(Gr={focusedElem:e,selectionRange:l},pu=!1,Fe=t;Fe!==null;)if(t=Fe,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,Fe=e;else for(;Fe!==null;){switch(t=Fe,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,l=t,n=u.memoizedProps,u=u.memoizedState,a=l.stateNode;try{var ue=la(l.type,n,l.elementType===l.type);e=a.getSnapshotBeforeUpdate(ue,u),a.__reactInternalSnapshotBeforeUpdate=e}catch(ae){we(l,l.return,ae)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,l=e.nodeType,l===9)Xr(e);else if(l===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Xr(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(c(163))}if(e=t.sibling,e!==null){e.return=t.return,Fe=e;break}Fe=t.return}}function Ed(e,t,l){var a=l.flags;switch(l.tag){case 0:case 11:case 15:Al(e,l),a&4&&Ln(5,l);break;case 1:if(Al(e,l),a&4)if(e=l.stateNode,t===null)try{e.componentDidMount()}catch(o){we(l,l.return,o)}else{var n=la(l.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(o){we(l,l.return,o)}}a&64&&gd(l),a&512&&qn(l,l.return);break;case 3:if(Al(e,l),a&64&&(e=l.updateQueue,e!==null)){if(t=null,l.child!==null)switch(l.child.tag){case 27:case 5:t=l.child.stateNode;break;case 1:t=l.child.stateNode}try{lf(e,t)}catch(o){we(l,l.return,o)}}break;case 27:t===null&&a&4&&xd(l);case 26:case 5:Al(e,l),t===null&&a&4&&pd(l),a&512&&qn(l,l.return);break;case 12:Al(e,l);break;case 13:Al(e,l),a&4&&Ad(e,l),a&64&&(e=l.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(l=B0.bind(null,l),ly(e,l))));break;case 22:if(a=l.memoizedState!==null||il,!a){t=t!==null&&t.memoizedState!==null||qe,n=il;var u=qe;il=a,(qe=t)&&!u?jl(e,l,(l.subtreeFlags&8772)!==0):Al(e,l),il=n,qe=u}break;case 30:break;default:Al(e,l)}}function Nd(e){var t=e.alternate;t!==null&&(e.alternate=null,Nd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&$u(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Oe=null,dt=!1;function ul(e,t,l){for(l=l.child;l!==null;)Td(e,t,l),l=l.sibling}function Td(e,t,l){if(vt&&typeof vt.onCommitFiberUnmount=="function")try{vt.onCommitFiberUnmount(cn,l)}catch{}switch(l.tag){case 26:qe||Vt(l,t),ul(e,t,l),l.memoizedState?l.memoizedState.count--:l.stateNode&&(l=l.stateNode,l.parentNode.removeChild(l));break;case 27:qe||Vt(l,t);var a=Oe,n=dt;Dl(l.type)&&(Oe=l.stateNode,dt=!1),ul(e,t,l),$n(l.stateNode),Oe=a,dt=n;break;case 5:qe||Vt(l,t);case 6:if(a=Oe,n=dt,Oe=null,ul(e,t,l),Oe=a,dt=n,Oe!==null)if(dt)try{(Oe.nodeType===9?Oe.body:Oe.nodeName==="HTML"?Oe.ownerDocument.body:Oe).removeChild(l.stateNode)}catch(u){we(l,t,u)}else try{Oe.removeChild(l.stateNode)}catch(u){we(l,t,u)}break;case 18:Oe!==null&&(dt?(e=Oe,dm(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,l.stateNode),ai(e)):dm(Oe,l.stateNode));break;case 4:a=Oe,n=dt,Oe=l.stateNode.containerInfo,dt=!0,ul(e,t,l),Oe=a,dt=n;break;case 0:case 11:case 14:case 15:qe||Tl(2,l,t),qe||Tl(4,l,t),ul(e,t,l);break;case 1:qe||(Vt(l,t),a=l.stateNode,typeof a.componentWillUnmount=="function"&&yd(l,t,a)),ul(e,t,l);break;case 21:ul(e,t,l);break;case 22:qe=(a=qe)||l.memoizedState!==null,ul(e,t,l),qe=a;break;default:ul(e,t,l)}}function Ad(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{ai(e)}catch(l){we(t,t.return,l)}}function w0(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Sd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Sd),t;default:throw Error(c(435,e.tag))}}function yr(e,t){var l=w0(e);t.forEach(function(a){var n=L0.bind(null,e,a);l.has(a)||(l.add(a),a.then(n,n))})}function bt(e,t){var l=t.deletions;if(l!==null)for(var a=0;a<l.length;a++){var n=l[a],u=e,o=t,m=o;e:for(;m!==null;){switch(m.tag){case 27:if(Dl(m.type)){Oe=m.stateNode,dt=!1;break e}break;case 5:Oe=m.stateNode,dt=!1;break e;case 3:case 4:Oe=m.stateNode.containerInfo,dt=!0;break e}m=m.return}if(Oe===null)throw Error(c(160));Td(u,o,n),Oe=null,dt=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)jd(t,e),t=t.sibling}var Ut=null;function jd(e,t){var l=e.alternate,a=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:bt(t,e),xt(e),a&4&&(Tl(3,e,e.return),Ln(3,e),Tl(5,e,e.return));break;case 1:bt(t,e),xt(e),a&512&&(qe||l===null||Vt(l,l.return)),a&64&&il&&(e=e.updateQueue,e!==null&&(a=e.callbacks,a!==null&&(l=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=l===null?a:l.concat(a))));break;case 26:var n=Ut;if(bt(t,e),xt(e),a&512&&(qe||l===null||Vt(l,l.return)),a&4){var u=l!==null?l.memoizedState:null;if(a=e.memoizedState,l===null)if(a===null)if(e.stateNode===null){e:{a=e.type,l=e.memoizedProps,n=n.ownerDocument||n;t:switch(a){case"title":u=n.getElementsByTagName("title")[0],(!u||u[on]||u[at]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(a),n.head.insertBefore(u,n.querySelector("head > title"))),tt(u,a,l),u[at]=e,$e(u),a=u;break e;case"link":var o=xm("link","href",n).get(a+(l.href||""));if(o){for(var m=0;m<o.length;m++)if(u=o[m],u.getAttribute("href")===(l.href==null||l.href===""?null:l.href)&&u.getAttribute("rel")===(l.rel==null?null:l.rel)&&u.getAttribute("title")===(l.title==null?null:l.title)&&u.getAttribute("crossorigin")===(l.crossOrigin==null?null:l.crossOrigin)){o.splice(m,1);break t}}u=n.createElement(a),tt(u,a,l),n.head.appendChild(u);break;case"meta":if(o=xm("meta","content",n).get(a+(l.content||""))){for(m=0;m<o.length;m++)if(u=o[m],u.getAttribute("content")===(l.content==null?null:""+l.content)&&u.getAttribute("name")===(l.name==null?null:l.name)&&u.getAttribute("property")===(l.property==null?null:l.property)&&u.getAttribute("http-equiv")===(l.httpEquiv==null?null:l.httpEquiv)&&u.getAttribute("charset")===(l.charSet==null?null:l.charSet)){o.splice(m,1);break t}}u=n.createElement(a),tt(u,a,l),n.head.appendChild(u);break;default:throw Error(c(468,a))}u[at]=e,$e(u),a=u}e.stateNode=a}else Sm(n,e.type,e.stateNode);else e.stateNode=bm(n,a,e.memoizedProps);else u!==a?(u===null?l.stateNode!==null&&(l=l.stateNode,l.parentNode.removeChild(l)):u.count--,a===null?Sm(n,e.type,e.stateNode):bm(n,a,e.memoizedProps)):a===null&&e.stateNode!==null&&mr(e,e.memoizedProps,l.memoizedProps)}break;case 27:bt(t,e),xt(e),a&512&&(qe||l===null||Vt(l,l.return)),l!==null&&a&4&&mr(e,e.memoizedProps,l.memoizedProps);break;case 5:if(bt(t,e),xt(e),a&512&&(qe||l===null||Vt(l,l.return)),e.flags&32){n=e.stateNode;try{va(n,"")}catch(C){we(e,e.return,C)}}a&4&&e.stateNode!=null&&(n=e.memoizedProps,mr(e,n,l!==null?l.memoizedProps:n)),a&1024&&(gr=!0);break;case 6:if(bt(t,e),xt(e),a&4){if(e.stateNode===null)throw Error(c(162));a=e.memoizedProps,l=e.stateNode;try{l.nodeValue=a}catch(C){we(e,e.return,C)}}break;case 3:if(vu=null,n=Ut,Ut=mu(t.containerInfo),bt(t,e),Ut=n,xt(e),a&4&&l!==null&&l.memoizedState.isDehydrated)try{ai(t.containerInfo)}catch(C){we(e,e.return,C)}gr&&(gr=!1,_d(e));break;case 4:a=Ut,Ut=mu(e.stateNode.containerInfo),bt(t,e),xt(e),Ut=a;break;case 12:bt(t,e),xt(e);break;case 13:bt(t,e),xt(e),e.child.flags&8192&&e.memoizedState!==null!=(l!==null&&l.memoizedState!==null)&&(Nr=qt()),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,yr(e,a)));break;case 22:n=e.memoizedState!==null;var p=l!==null&&l.memoizedState!==null,M=il,k=qe;if(il=M||n,qe=k||p,bt(t,e),qe=k,il=M,xt(e),a&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(l===null||p||il||qe||aa(e)),l=null,t=e;;){if(t.tag===5||t.tag===26){if(l===null){p=l=t;try{if(u=p.stateNode,n)o=u.style,typeof o.setProperty=="function"?o.setProperty("display","none","important"):o.display="none";else{m=p.stateNode;var q=p.memoizedProps.style,w=q!=null&&q.hasOwnProperty("display")?q.display:null;m.style.display=w==null||typeof w=="boolean"?"":(""+w).trim()}}catch(C){we(p,p.return,C)}}}else if(t.tag===6){if(l===null){p=t;try{p.stateNode.nodeValue=n?"":p.memoizedProps}catch(C){we(p,p.return,C)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;l===t&&(l=null),t=t.return}l===t&&(l=null),t.sibling.return=t.return,t=t.sibling}a&4&&(a=e.updateQueue,a!==null&&(l=a.retryQueue,l!==null&&(a.retryQueue=null,yr(e,l))));break;case 19:bt(t,e),xt(e),a&4&&(a=e.updateQueue,a!==null&&(e.updateQueue=null,yr(e,a)));break;case 30:break;case 21:break;default:bt(t,e),xt(e)}}function xt(e){var t=e.flags;if(t&2){try{for(var l,a=e.return;a!==null;){if(bd(a)){l=a;break}a=a.return}if(l==null)throw Error(c(160));switch(l.tag){case 27:var n=l.stateNode,u=hr(e);eu(e,u,n);break;case 5:var o=l.stateNode;l.flags&32&&(va(o,""),l.flags&=-33);var m=hr(e);eu(e,m,o);break;case 3:case 4:var p=l.stateNode.containerInfo,M=hr(e);vr(e,M,p);break;default:throw Error(c(161))}}catch(k){we(e,e.return,k)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function _d(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;_d(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Al(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Ed(e,t.alternate,t),t=t.sibling}function aa(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Tl(4,t,t.return),aa(t);break;case 1:Vt(t,t.return);var l=t.stateNode;typeof l.componentWillUnmount=="function"&&yd(t,t.return,l),aa(t);break;case 27:$n(t.stateNode);case 26:case 5:Vt(t,t.return),aa(t);break;case 22:t.memoizedState===null&&aa(t);break;case 30:aa(t);break;default:aa(t)}e=e.sibling}}function jl(e,t,l){for(l=l&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var a=t.alternate,n=e,u=t,o=u.flags;switch(u.tag){case 0:case 11:case 15:jl(n,u,l),Ln(4,u);break;case 1:if(jl(n,u,l),a=u,n=a.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(M){we(a,a.return,M)}if(a=u,n=a.updateQueue,n!==null){var m=a.stateNode;try{var p=n.shared.hiddenCallbacks;if(p!==null)for(n.shared.hiddenCallbacks=null,n=0;n<p.length;n++)tf(p[n],m)}catch(M){we(a,a.return,M)}}l&&o&64&&gd(u),qn(u,u.return);break;case 27:xd(u);case 26:case 5:jl(n,u,l),l&&a===null&&o&4&&pd(u),qn(u,u.return);break;case 12:jl(n,u,l);break;case 13:jl(n,u,l),l&&o&4&&Ad(n,u);break;case 22:u.memoizedState===null&&jl(n,u,l),qn(u,u.return);break;case 30:break;default:jl(n,u,l)}t=t.sibling}}function pr(e,t){var l=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==l&&(e!=null&&e.refCount++,l!=null&&An(l))}function br(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&An(e))}function Xt(e,t,l,a){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Md(e,t,l,a),t=t.sibling}function Md(e,t,l,a){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Xt(e,t,l,a),n&2048&&Ln(9,t);break;case 1:Xt(e,t,l,a);break;case 3:Xt(e,t,l,a),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&An(e)));break;case 12:if(n&2048){Xt(e,t,l,a),e=t.stateNode;try{var u=t.memoizedProps,o=u.id,m=u.onPostCommit;typeof m=="function"&&m(o,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(p){we(t,t.return,p)}}else Xt(e,t,l,a);break;case 13:Xt(e,t,l,a);break;case 23:break;case 22:u=t.stateNode,o=t.alternate,t.memoizedState!==null?u._visibility&2?Xt(e,t,l,a):Gn(e,t):u._visibility&2?Xt(e,t,l,a):(u._visibility|=2,za(e,t,l,a,(t.subtreeFlags&10256)!==0)),n&2048&&pr(o,t);break;case 24:Xt(e,t,l,a),n&2048&&br(t.alternate,t);break;default:Xt(e,t,l,a)}}function za(e,t,l,a,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,o=t,m=l,p=a,M=o.flags;switch(o.tag){case 0:case 11:case 15:za(u,o,m,p,n),Ln(8,o);break;case 23:break;case 22:var k=o.stateNode;o.memoizedState!==null?k._visibility&2?za(u,o,m,p,n):Gn(u,o):(k._visibility|=2,za(u,o,m,p,n)),n&&M&2048&&pr(o.alternate,o);break;case 24:za(u,o,m,p,n),n&&M&2048&&br(o.alternate,o);break;default:za(u,o,m,p,n)}t=t.sibling}}function Gn(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var l=e,a=t,n=a.flags;switch(a.tag){case 22:Gn(l,a),n&2048&&pr(a.alternate,a);break;case 24:Gn(l,a),n&2048&&br(a.alternate,a);break;default:Gn(l,a)}t=t.sibling}}var Yn=8192;function Ua(e){if(e.subtreeFlags&Yn)for(e=e.child;e!==null;)Rd(e),e=e.sibling}function Rd(e){switch(e.tag){case 26:Ua(e),e.flags&Yn&&e.memoizedState!==null&&vy(Ut,e.memoizedState,e.memoizedProps);break;case 5:Ua(e);break;case 3:case 4:var t=Ut;Ut=mu(e.stateNode.containerInfo),Ua(e),Ut=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Yn,Yn=16777216,Ua(e),Yn=t):Ua(e));break;default:Ua(e)}}function wd(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Vn(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Fe=a,Dd(a,e)}wd(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Cd(e),e=e.sibling}function Cd(e){switch(e.tag){case 0:case 11:case 15:Vn(e),e.flags&2048&&Tl(9,e,e.return);break;case 3:Vn(e);break;case 12:Vn(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,tu(e)):Vn(e);break;default:Vn(e)}}function tu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var l=0;l<t.length;l++){var a=t[l];Fe=a,Dd(a,e)}wd(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Tl(8,t,t.return),tu(t);break;case 22:l=t.stateNode,l._visibility&2&&(l._visibility&=-3,tu(t));break;default:tu(t)}e=e.sibling}}function Dd(e,t){for(;Fe!==null;){var l=Fe;switch(l.tag){case 0:case 11:case 15:Tl(8,l,t);break;case 23:case 22:if(l.memoizedState!==null&&l.memoizedState.cachePool!==null){var a=l.memoizedState.cachePool.pool;a!=null&&a.refCount++}break;case 24:An(l.memoizedState.cache)}if(a=l.child,a!==null)a.return=l,Fe=a;else e:for(l=e;Fe!==null;){a=Fe;var n=a.sibling,u=a.return;if(Nd(a),a===l){Fe=null;break e}if(n!==null){n.return=u,Fe=n;break e}Fe=u}}}var C0={getCacheForType:function(e){var t=nt(Xe),l=t.data.get(e);return l===void 0&&(l=e(),t.data.set(e,l)),l}},D0=typeof WeakMap=="function"?WeakMap:Map,Te=0,Ce=null,ve=null,ye=0,Ae=0,St=null,_l=!1,Ha=!1,xr=!1,cl=0,Be=0,Ml=0,na=0,Sr=0,Dt=0,ka=0,Xn=null,mt=null,Er=!1,Nr=0,lu=1/0,au=null,Rl=null,et=0,wl=null,Ba=null,La=0,Tr=0,Ar=null,Od=null,Qn=0,jr=null;function Et(){if((Te&2)!==0&&ye!==0)return ye&-ye;if(D.T!==null){var e=ja;return e!==0?e:Or()}return Ks()}function zd(){Dt===0&&(Dt=(ye&536870912)===0||Ee?Vs():536870912);var e=Ct.current;return e!==null&&(e.flags|=32),Dt}function Nt(e,t,l){(e===Ce&&(Ae===2||Ae===9)||e.cancelPendingCommit!==null)&&(qa(e,0),Cl(e,ye,Dt,!1)),sn(e,l),((Te&2)===0||e!==Ce)&&(e===Ce&&((Te&2)===0&&(na|=l),Be===4&&Cl(e,ye,Dt,!1)),Qt(e))}function Ud(e,t,l){if((Te&6)!==0)throw Error(c(327));var a=!l&&(t&124)===0&&(t&e.expiredLanes)===0||rn(e,t),n=a?U0(e,t):Rr(e,t,!0),u=a;do{if(n===0){Ha&&!a&&Cl(e,t,0,!1);break}else{if(l=e.current.alternate,u&&!O0(l)){n=Rr(e,t,!1),u=!1;continue}if(n===2){if(u=t,e.errorRecoveryDisabledLanes&u)var o=0;else o=e.pendingLanes&-536870913,o=o!==0?o:o&536870912?536870912:0;if(o!==0){t=o;e:{var m=e;n=Xn;var p=m.current.memoizedState.isDehydrated;if(p&&(qa(m,o).flags|=256),o=Rr(m,o,!1),o!==2){if(xr&&!p){m.errorRecoveryDisabledLanes|=u,na|=u,n=4;break e}u=mt,mt=n,u!==null&&(mt===null?mt=u:mt.push.apply(mt,u))}n=o}if(u=!1,n!==2)continue}}if(n===1){qa(e,0),Cl(e,t,0,!0);break}e:{switch(a=e,u=n,u){case 0:case 1:throw Error(c(345));case 4:if((t&4194048)!==t)break;case 6:Cl(a,t,Dt,!_l);break e;case 2:mt=null;break;case 3:case 5:break;default:throw Error(c(329))}if((t&62914560)===t&&(n=Nr+300-qt(),10<n)){if(Cl(a,t,Dt,!_l),hi(a,0,!0)!==0)break e;a.timeoutHandle=om(Hd.bind(null,a,l,mt,au,Er,t,Dt,na,ka,_l,u,2,-0,0),n);break e}Hd(a,l,mt,au,Er,t,Dt,na,ka,_l,u,0,-0,0)}}break}while(!0);Qt(e)}function Hd(e,t,l,a,n,u,o,m,p,M,k,q,w,C){if(e.timeoutHandle=-1,q=t.subtreeFlags,(q&8192||(q&16785408)===16785408)&&(Pn={stylesheets:null,count:0,unsuspend:hy},Rd(t),q=gy(),q!==null)){e.cancelPendingCommit=q(Vd.bind(null,e,t,u,l,a,n,o,m,p,k,1,w,C)),Cl(e,u,o,!M);return}Vd(e,t,u,l,a,n,o,m,p)}function O0(e){for(var t=e;;){var l=t.tag;if((l===0||l===11||l===15)&&t.flags&16384&&(l=t.updateQueue,l!==null&&(l=l.stores,l!==null)))for(var a=0;a<l.length;a++){var n=l[a],u=n.getSnapshot;n=n.value;try{if(!yt(u(),n))return!1}catch{return!1}}if(l=t.child,t.subtreeFlags&16384&&l!==null)l.return=t,t=l;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Cl(e,t,l,a){t&=~Sr,t&=~na,e.suspendedLanes|=t,e.pingedLanes&=~t,a&&(e.warmLanes|=t),a=e.expirationTimes;for(var n=t;0<n;){var u=31-gt(n),o=1<<u;a[u]=-1,n&=~o}l!==0&&Qs(e,l,t)}function nu(){return(Te&6)===0?(Zn(0),!1):!0}function _r(){if(ve!==null){if(Ae===0)var e=ve.return;else e=ve,It=Pl=null,Xc(e),Da=null,Hn=0,e=ve;for(;e!==null;)vd(e.alternate,e),e=e.return;ve=null}}function qa(e,t){var l=e.timeoutHandle;l!==-1&&(e.timeoutHandle=-1,F0(l)),l=e.cancelPendingCommit,l!==null&&(e.cancelPendingCommit=null,l()),_r(),Ce=e,ve=l=Wt(e.current,null),ye=t,Ae=0,St=null,_l=!1,Ha=rn(e,t),xr=!1,ka=Dt=Sr=na=Ml=Be=0,mt=Xn=null,Er=!1,(t&8)!==0&&(t|=t&32);var a=e.entangledLanes;if(a!==0)for(e=e.entanglements,a&=t;0<a;){var n=31-gt(a),u=1<<n;t|=e[n],a&=~u}return cl=t,ji(),l}function kd(e,t){fe=null,D.H=Qi,t===_n||t===Ui?(t=Io(),Ae=3):t===Wo?(t=Io(),Ae=4):Ae=t===td?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,St=t,ve===null&&(Be=1,Wi(e,_t(t,e.current)))}function Bd(){var e=D.H;return D.H=Qi,e===null?Qi:e}function Ld(){var e=D.A;return D.A=C0,e}function Mr(){Be=4,_l||(ye&4194048)!==ye&&Ct.current!==null||(Ha=!0),(Ml&134217727)===0&&(na&134217727)===0||Ce===null||Cl(Ce,ye,Dt,!1)}function Rr(e,t,l){var a=Te;Te|=2;var n=Bd(),u=Ld();(Ce!==e||ye!==t)&&(au=null,qa(e,t)),t=!1;var o=Be;e:do try{if(Ae!==0&&ve!==null){var m=ve,p=St;switch(Ae){case 8:_r(),o=6;break e;case 3:case 2:case 9:case 6:Ct.current===null&&(t=!0);var M=Ae;if(Ae=0,St=null,Ga(e,m,p,M),l&&Ha){o=0;break e}break;default:M=Ae,Ae=0,St=null,Ga(e,m,p,M)}}z0(),o=Be;break}catch(k){kd(e,k)}while(!0);return t&&e.shellSuspendCounter++,It=Pl=null,Te=a,D.H=n,D.A=u,ve===null&&(Ce=null,ye=0,ji()),o}function z0(){for(;ve!==null;)qd(ve)}function U0(e,t){var l=Te;Te|=2;var a=Bd(),n=Ld();Ce!==e||ye!==t?(au=null,lu=qt()+500,qa(e,t)):Ha=rn(e,t);e:do try{if(Ae!==0&&ve!==null){t=ve;var u=St;t:switch(Ae){case 1:Ae=0,St=null,Ga(e,t,u,1);break;case 2:case 9:if(Fo(u)){Ae=0,St=null,Gd(t);break}t=function(){Ae!==2&&Ae!==9||Ce!==e||(Ae=7),Qt(e)},u.then(t,t);break e;case 3:Ae=7;break e;case 4:Ae=5;break e;case 7:Fo(u)?(Ae=0,St=null,Gd(t)):(Ae=0,St=null,Ga(e,t,u,7));break;case 5:var o=null;switch(ve.tag){case 26:o=ve.memoizedState;case 5:case 27:var m=ve;if(!o||Em(o)){Ae=0,St=null;var p=m.sibling;if(p!==null)ve=p;else{var M=m.return;M!==null?(ve=M,iu(M)):ve=null}break t}}Ae=0,St=null,Ga(e,t,u,5);break;case 6:Ae=0,St=null,Ga(e,t,u,6);break;case 8:_r(),Be=6;break e;default:throw Error(c(462))}}H0();break}catch(k){kd(e,k)}while(!0);return It=Pl=null,D.H=a,D.A=n,Te=l,ve!==null?0:(Ce=null,ye=0,ji(),Be)}function H0(){for(;ve!==null&&!ng();)qd(ve)}function qd(e){var t=md(e.alternate,e,cl);e.memoizedProps=e.pendingProps,t===null?iu(e):ve=t}function Gd(e){var t=e,l=t.alternate;switch(t.tag){case 15:case 0:t=cd(l,t,t.pendingProps,t.type,void 0,ye);break;case 11:t=cd(l,t,t.pendingProps,t.type.render,t.ref,ye);break;case 5:Xc(t);default:vd(l,t),t=ve=Go(t,cl),t=md(l,t,cl)}e.memoizedProps=e.pendingProps,t===null?iu(e):ve=t}function Ga(e,t,l,a){It=Pl=null,Xc(t),Da=null,Hn=0;var n=t.return;try{if(A0(e,n,t,l,ye)){Be=1,Wi(e,_t(l,e.current)),ve=null;return}}catch(u){if(n!==null)throw ve=n,u;Be=1,Wi(e,_t(l,e.current)),ve=null;return}t.flags&32768?(Ee||a===1?e=!0:Ha||(ye&536870912)!==0?e=!1:(_l=e=!0,(a===2||a===9||a===3||a===6)&&(a=Ct.current,a!==null&&a.tag===13&&(a.flags|=16384))),Yd(t,e)):iu(t)}function iu(e){var t=e;do{if((t.flags&32768)!==0){Yd(t,_l);return}e=t.return;var l=_0(t.alternate,t,cl);if(l!==null){ve=l;return}if(t=t.sibling,t!==null){ve=t;return}ve=t=e}while(t!==null);Be===0&&(Be=5)}function Yd(e,t){do{var l=M0(e.alternate,e);if(l!==null){l.flags&=32767,ve=l;return}if(l=e.return,l!==null&&(l.flags|=32768,l.subtreeFlags=0,l.deletions=null),!t&&(e=e.sibling,e!==null)){ve=e;return}ve=e=l}while(e!==null);Be=6,ve=null}function Vd(e,t,l,a,n,u,o,m,p){e.cancelPendingCommit=null;do uu();while(et!==0);if((Te&6)!==0)throw Error(c(327));if(t!==null){if(t===e.current)throw Error(c(177));if(u=t.lanes|t.childLanes,u|=bc,hg(e,l,u,o,m,p),e===Ce&&(ve=Ce=null,ye=0),Ba=t,wl=e,La=l,Tr=u,Ar=n,Od=a,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,q0(fi,function(){return Jd(),null})):(e.callbackNode=null,e.callbackPriority=0),a=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||a){a=D.T,D.T=null,n=K.p,K.p=2,o=Te,Te|=4;try{R0(e,t,l)}finally{Te=o,K.p=n,D.T=a}}et=1,Xd(),Qd(),Zd()}}function Xd(){if(et===1){et=0;var e=wl,t=Ba,l=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||l){l=D.T,D.T=null;var a=K.p;K.p=2;var n=Te;Te|=4;try{jd(t,e);var u=Gr,o=Co(e.containerInfo),m=u.focusedElem,p=u.selectionRange;if(o!==m&&m&&m.ownerDocument&&wo(m.ownerDocument.documentElement,m)){if(p!==null&&hc(m)){var M=p.start,k=p.end;if(k===void 0&&(k=M),"selectionStart"in m)m.selectionStart=M,m.selectionEnd=Math.min(k,m.value.length);else{var q=m.ownerDocument||document,w=q&&q.defaultView||window;if(w.getSelection){var C=w.getSelection(),ue=m.textContent.length,ae=Math.min(p.start,ue),Me=p.end===void 0?ae:Math.min(p.end,ue);!C.extend&&ae>Me&&(o=Me,Me=ae,ae=o);var A=Ro(m,ae),N=Ro(m,Me);if(A&&N&&(C.rangeCount!==1||C.anchorNode!==A.node||C.anchorOffset!==A.offset||C.focusNode!==N.node||C.focusOffset!==N.offset)){var _=q.createRange();_.setStart(A.node,A.offset),C.removeAllRanges(),ae>Me?(C.addRange(_),C.extend(N.node,N.offset)):(_.setEnd(N.node,N.offset),C.addRange(_))}}}}for(q=[],C=m;C=C.parentNode;)C.nodeType===1&&q.push({element:C,left:C.scrollLeft,top:C.scrollTop});for(typeof m.focus=="function"&&m.focus(),m=0;m<q.length;m++){var B=q[m];B.element.scrollLeft=B.left,B.element.scrollTop=B.top}}pu=!!qr,Gr=qr=null}finally{Te=n,K.p=a,D.T=l}}e.current=t,et=2}}function Qd(){if(et===2){et=0;var e=wl,t=Ba,l=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||l){l=D.T,D.T=null;var a=K.p;K.p=2;var n=Te;Te|=4;try{Ed(e,t.alternate,t)}finally{Te=n,K.p=a,D.T=l}}et=3}}function Zd(){if(et===4||et===3){et=0,ig();var e=wl,t=Ba,l=La,a=Od;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?et=5:(et=0,Ba=wl=null,Kd(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(Rl=null),Ku(l),t=t.stateNode,vt&&typeof vt.onCommitFiberRoot=="function")try{vt.onCommitFiberRoot(cn,t,void 0,(t.current.flags&128)===128)}catch{}if(a!==null){t=D.T,n=K.p,K.p=2,D.T=null;try{for(var u=e.onRecoverableError,o=0;o<a.length;o++){var m=a[o];u(m.value,{componentStack:m.stack})}}finally{D.T=t,K.p=n}}(La&3)!==0&&uu(),Qt(e),n=e.pendingLanes,(l&4194090)!==0&&(n&42)!==0?e===jr?Qn++:(Qn=0,jr=e):Qn=0,Zn(0)}}function Kd(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,An(t)))}function uu(e){return Xd(),Qd(),Zd(),Jd()}function Jd(){if(et!==5)return!1;var e=wl,t=Tr;Tr=0;var l=Ku(La),a=D.T,n=K.p;try{K.p=32>l?32:l,D.T=null,l=Ar,Ar=null;var u=wl,o=La;if(et=0,Ba=wl=null,La=0,(Te&6)!==0)throw Error(c(331));var m=Te;if(Te|=4,Cd(u.current),Md(u,u.current,o,l),Te=m,Zn(0,!1),vt&&typeof vt.onPostCommitFiberRoot=="function")try{vt.onPostCommitFiberRoot(cn,u)}catch{}return!0}finally{K.p=n,D.T=a,Kd(e,t)}}function $d(e,t,l){t=_t(l,t),t=nr(e.stateNode,t,2),e=xl(e,t,2),e!==null&&(sn(e,2),Qt(e))}function we(e,t,l){if(e.tag===3)$d(e,e,l);else for(;t!==null;){if(t.tag===3){$d(t,e,l);break}else if(t.tag===1){var a=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof a.componentDidCatch=="function"&&(Rl===null||!Rl.has(a))){e=_t(l,e),l=If(2),a=xl(t,l,2),a!==null&&(ed(l,a,t,e),sn(a,2),Qt(a));break}}t=t.return}}function wr(e,t,l){var a=e.pingCache;if(a===null){a=e.pingCache=new D0;var n=new Set;a.set(t,n)}else n=a.get(t),n===void 0&&(n=new Set,a.set(t,n));n.has(l)||(xr=!0,n.add(l),e=k0.bind(null,e,t,l),t.then(e,e))}function k0(e,t,l){var a=e.pingCache;a!==null&&a.delete(t),e.pingedLanes|=e.suspendedLanes&l,e.warmLanes&=~l,Ce===e&&(ye&l)===l&&(Be===4||Be===3&&(ye&62914560)===ye&&300>qt()-Nr?(Te&2)===0&&qa(e,0):Sr|=l,ka===ye&&(ka=0)),Qt(e)}function Wd(e,t){t===0&&(t=Xs()),e=Ea(e,t),e!==null&&(sn(e,t),Qt(e))}function B0(e){var t=e.memoizedState,l=0;t!==null&&(l=t.retryLane),Wd(e,l)}function L0(e,t){var l=0;switch(e.tag){case 13:var a=e.stateNode,n=e.memoizedState;n!==null&&(l=n.retryLane);break;case 19:a=e.stateNode;break;case 22:a=e.stateNode._retryCache;break;default:throw Error(c(314))}a!==null&&a.delete(t),Wd(e,l)}function q0(e,t){return Vu(e,t)}var cu=null,Ya=null,Cr=!1,ru=!1,Dr=!1,ia=0;function Qt(e){e!==Ya&&e.next===null&&(Ya===null?cu=Ya=e:Ya=Ya.next=e),ru=!0,Cr||(Cr=!0,Y0())}function Zn(e,t){if(!Dr&&ru){Dr=!0;do for(var l=!1,a=cu;a!==null;){if(e!==0){var n=a.pendingLanes;if(n===0)var u=0;else{var o=a.suspendedLanes,m=a.pingedLanes;u=(1<<31-gt(42|e)+1)-1,u&=n&~(o&~m),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(l=!0,em(a,u))}else u=ye,u=hi(a,a===Ce?u:0,a.cancelPendingCommit!==null||a.timeoutHandle!==-1),(u&3)===0||rn(a,u)||(l=!0,em(a,u));a=a.next}while(l);Dr=!1}}function G0(){Fd()}function Fd(){ru=Cr=!1;var e=0;ia!==0&&(W0()&&(e=ia),ia=0);for(var t=qt(),l=null,a=cu;a!==null;){var n=a.next,u=Pd(a,t);u===0?(a.next=null,l===null?cu=n:l.next=n,n===null&&(Ya=l)):(l=a,(e!==0||(u&3)!==0)&&(ru=!0)),a=n}Zn(e)}function Pd(e,t){for(var l=e.suspendedLanes,a=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var o=31-gt(u),m=1<<o,p=n[o];p===-1?((m&l)===0||(m&a)!==0)&&(n[o]=mg(m,t)):p<=t&&(e.expiredLanes|=m),u&=~m}if(t=Ce,l=ye,l=hi(e,e===t?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a=e.callbackNode,l===0||e===t&&(Ae===2||Ae===9)||e.cancelPendingCommit!==null)return a!==null&&a!==null&&Xu(a),e.callbackNode=null,e.callbackPriority=0;if((l&3)===0||rn(e,l)){if(t=l&-l,t===e.callbackPriority)return t;switch(a!==null&&Xu(a),Ku(l)){case 2:case 8:l=Gs;break;case 32:l=fi;break;case 268435456:l=Ys;break;default:l=fi}return a=Id.bind(null,e),l=Vu(l,a),e.callbackPriority=t,e.callbackNode=l,t}return a!==null&&a!==null&&Xu(a),e.callbackPriority=2,e.callbackNode=null,2}function Id(e,t){if(et!==0&&et!==5)return e.callbackNode=null,e.callbackPriority=0,null;var l=e.callbackNode;if(uu()&&e.callbackNode!==l)return null;var a=ye;return a=hi(e,e===Ce?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),a===0?null:(Ud(e,a,t),Pd(e,qt()),e.callbackNode!=null&&e.callbackNode===l?Id.bind(null,e):null)}function em(e,t){if(uu())return null;Ud(e,t,!0)}function Y0(){P0(function(){(Te&6)!==0?Vu(qs,G0):Fd()})}function Or(){return ia===0&&(ia=Vs()),ia}function tm(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:bi(""+e)}function lm(e,t){var l=t.ownerDocument.createElement("input");return l.name=t.name,l.value=t.value,e.id&&l.setAttribute("form",e.id),t.parentNode.insertBefore(l,t),e=new FormData(e),l.parentNode.removeChild(l),e}function V0(e,t,l,a,n){if(t==="submit"&&l&&l.stateNode===n){var u=tm((n[st]||null).action),o=a.submitter;o&&(t=(t=o[st]||null)?tm(t.formAction):o.getAttribute("formAction"),t!==null&&(u=t,o=null));var m=new Ni("action","action",null,a,n);e.push({event:m,listeners:[{instance:null,listener:function(){if(a.defaultPrevented){if(ia!==0){var p=o?lm(n,o):new FormData(n);Ic(l,{pending:!0,data:p,method:n.method,action:u},null,p)}}else typeof u=="function"&&(m.preventDefault(),p=o?lm(n,o):new FormData(n),Ic(l,{pending:!0,data:p,method:n.method,action:u},u,p))},currentTarget:n}]})}}for(var zr=0;zr<pc.length;zr++){var Ur=pc[zr],X0=Ur.toLowerCase(),Q0=Ur[0].toUpperCase()+Ur.slice(1);zt(X0,"on"+Q0)}zt(zo,"onAnimationEnd"),zt(Uo,"onAnimationIteration"),zt(Ho,"onAnimationStart"),zt("dblclick","onDoubleClick"),zt("focusin","onFocus"),zt("focusout","onBlur"),zt(r0,"onTransitionRun"),zt(s0,"onTransitionStart"),zt(o0,"onTransitionCancel"),zt(ko,"onTransitionEnd"),da("onMouseEnter",["mouseout","mouseover"]),da("onMouseLeave",["mouseout","mouseover"]),da("onPointerEnter",["pointerout","pointerover"]),da("onPointerLeave",["pointerout","pointerover"]),Vl("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),Vl("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),Vl("onBeforeInput",["compositionend","keypress","textInput","paste"]),Vl("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),Vl("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),Vl("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Kn="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Z0=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Kn));function am(e,t){t=(t&4)!==0;for(var l=0;l<e.length;l++){var a=e[l],n=a.event;a=a.listeners;e:{var u=void 0;if(t)for(var o=a.length-1;0<=o;o--){var m=a[o],p=m.instance,M=m.currentTarget;if(m=m.listener,p!==u&&n.isPropagationStopped())break e;u=m,n.currentTarget=M;try{u(n)}catch(k){$i(k)}n.currentTarget=null,u=p}else for(o=0;o<a.length;o++){if(m=a[o],p=m.instance,M=m.currentTarget,m=m.listener,p!==u&&n.isPropagationStopped())break e;u=m,n.currentTarget=M;try{u(n)}catch(k){$i(k)}n.currentTarget=null,u=p}}}}function ge(e,t){var l=t[Ju];l===void 0&&(l=t[Ju]=new Set);var a=e+"__bubble";l.has(a)||(nm(t,e,2,!1),l.add(a))}function Hr(e,t,l){var a=0;t&&(a|=4),nm(l,e,a,t)}var su="_reactListening"+Math.random().toString(36).slice(2);function kr(e){if(!e[su]){e[su]=!0,$s.forEach(function(l){l!=="selectionchange"&&(Z0.has(l)||Hr(l,!1,e),Hr(l,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[su]||(t[su]=!0,Hr("selectionchange",!1,t))}}function nm(e,t,l,a){switch(Mm(t)){case 2:var n=by;break;case 8:n=xy;break;default:n=Fr}l=n.bind(null,t,l,e),n=void 0,!ic||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),a?n!==void 0?e.addEventListener(t,l,{capture:!0,passive:n}):e.addEventListener(t,l,!0):n!==void 0?e.addEventListener(t,l,{passive:n}):e.addEventListener(t,l,!1)}function Br(e,t,l,a,n){var u=a;if((t&1)===0&&(t&2)===0&&a!==null)e:for(;;){if(a===null)return;var o=a.tag;if(o===3||o===4){var m=a.stateNode.containerInfo;if(m===n)break;if(o===4)for(o=a.return;o!==null;){var p=o.tag;if((p===3||p===4)&&o.stateNode.containerInfo===n)return;o=o.return}for(;m!==null;){if(o=sa(m),o===null)return;if(p=o.tag,p===5||p===6||p===26||p===27){a=u=o;continue e}m=m.parentNode}}a=a.return}so(function(){var M=u,k=ac(l),q=[];e:{var w=Bo.get(e);if(w!==void 0){var C=Ni,ue=e;switch(e){case"keypress":if(Si(l)===0)break e;case"keydown":case"keyup":C=qg;break;case"focusin":ue="focus",C=sc;break;case"focusout":ue="blur",C=sc;break;case"beforeblur":case"afterblur":C=sc;break;case"click":if(l.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":C=mo;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":C=Mg;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":C=Vg;break;case zo:case Uo:case Ho:C=Cg;break;case ko:C=Qg;break;case"scroll":case"scrollend":C=jg;break;case"wheel":C=Kg;break;case"copy":case"cut":case"paste":C=Og;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":C=vo;break;case"toggle":case"beforetoggle":C=$g}var ae=(t&4)!==0,Me=!ae&&(e==="scroll"||e==="scrollend"),A=ae?w!==null?w+"Capture":null:w;ae=[];for(var N=M,_;N!==null;){var B=N;if(_=B.stateNode,B=B.tag,B!==5&&B!==26&&B!==27||_===null||A===null||(B=dn(N,A),B!=null&&ae.push(Jn(N,B,_))),Me)break;N=N.return}0<ae.length&&(w=new C(w,ue,null,l,k),q.push({event:w,listeners:ae}))}}if((t&7)===0){e:{if(w=e==="mouseover"||e==="pointerover",C=e==="mouseout"||e==="pointerout",w&&l!==lc&&(ue=l.relatedTarget||l.fromElement)&&(sa(ue)||ue[ra]))break e;if((C||w)&&(w=k.window===k?k:(w=k.ownerDocument)?w.defaultView||w.parentWindow:window,C?(ue=l.relatedTarget||l.toElement,C=M,ue=ue?sa(ue):null,ue!==null&&(Me=h(ue),ae=ue.tag,ue!==Me||ae!==5&&ae!==27&&ae!==6)&&(ue=null)):(C=null,ue=M),C!==ue)){if(ae=mo,B="onMouseLeave",A="onMouseEnter",N="mouse",(e==="pointerout"||e==="pointerover")&&(ae=vo,B="onPointerLeave",A="onPointerEnter",N="pointer"),Me=C==null?w:fn(C),_=ue==null?w:fn(ue),w=new ae(B,N+"leave",C,l,k),w.target=Me,w.relatedTarget=_,B=null,sa(k)===M&&(ae=new ae(A,N+"enter",ue,l,k),ae.target=_,ae.relatedTarget=Me,B=ae),Me=B,C&&ue)t:{for(ae=C,A=ue,N=0,_=ae;_;_=Va(_))N++;for(_=0,B=A;B;B=Va(B))_++;for(;0<N-_;)ae=Va(ae),N--;for(;0<_-N;)A=Va(A),_--;for(;N--;){if(ae===A||A!==null&&ae===A.alternate)break t;ae=Va(ae),A=Va(A)}ae=null}else ae=null;C!==null&&im(q,w,C,ae,!1),ue!==null&&Me!==null&&im(q,Me,ue,ae,!0)}}e:{if(w=M?fn(M):window,C=w.nodeName&&w.nodeName.toLowerCase(),C==="select"||C==="input"&&w.type==="file")var P=No;else if(So(w))if(To)P=i0;else{P=a0;var me=l0}else C=w.nodeName,!C||C.toLowerCase()!=="input"||w.type!=="checkbox"&&w.type!=="radio"?M&&tc(M.elementType)&&(P=No):P=n0;if(P&&(P=P(e,M))){Eo(q,P,l,k);break e}me&&me(e,w,M),e==="focusout"&&M&&w.type==="number"&&M.memoizedProps.value!=null&&ec(w,"number",w.value)}switch(me=M?fn(M):window,e){case"focusin":(So(me)||me.contentEditable==="true")&&(ba=me,vc=M,xn=null);break;case"focusout":xn=vc=ba=null;break;case"mousedown":gc=!0;break;case"contextmenu":case"mouseup":case"dragend":gc=!1,Do(q,l,k);break;case"selectionchange":if(c0)break;case"keydown":case"keyup":Do(q,l,k)}var te;if(fc)e:{switch(e){case"compositionstart":var ie="onCompositionStart";break e;case"compositionend":ie="onCompositionEnd";break e;case"compositionupdate":ie="onCompositionUpdate";break e}ie=void 0}else pa?bo(e,l)&&(ie="onCompositionEnd"):e==="keydown"&&l.keyCode===229&&(ie="onCompositionStart");ie&&(go&&l.locale!=="ko"&&(pa||ie!=="onCompositionStart"?ie==="onCompositionEnd"&&pa&&(te=oo()):(gl=k,uc="value"in gl?gl.value:gl.textContent,pa=!0)),me=ou(M,ie),0<me.length&&(ie=new ho(ie,e,null,l,k),q.push({event:ie,listeners:me}),te?ie.data=te:(te=xo(l),te!==null&&(ie.data=te)))),(te=Fg?Pg(e,l):Ig(e,l))&&(ie=ou(M,"onBeforeInput"),0<ie.length&&(me=new ho("onBeforeInput","beforeinput",null,l,k),q.push({event:me,listeners:ie}),me.data=te)),V0(q,e,M,l,k)}am(q,t)})}function Jn(e,t,l){return{instance:e,listener:t,currentTarget:l}}function ou(e,t){for(var l=t+"Capture",a=[];e!==null;){var n=e,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=dn(e,l),n!=null&&a.unshift(Jn(e,n,u)),n=dn(e,t),n!=null&&a.push(Jn(e,n,u))),e.tag===3)return a;e=e.return}return[]}function Va(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function im(e,t,l,a,n){for(var u=t._reactName,o=[];l!==null&&l!==a;){var m=l,p=m.alternate,M=m.stateNode;if(m=m.tag,p!==null&&p===a)break;m!==5&&m!==26&&m!==27||M===null||(p=M,n?(M=dn(l,u),M!=null&&o.unshift(Jn(l,M,p))):n||(M=dn(l,u),M!=null&&o.push(Jn(l,M,p)))),l=l.return}o.length!==0&&e.push({event:t,listeners:o})}var K0=/\r\n?/g,J0=/\u0000|\uFFFD/g;function um(e){return(typeof e=="string"?e:""+e).replace(K0,`
`).replace(J0,"")}function cm(e,t){return t=um(t),um(e)===t}function fu(){}function _e(e,t,l,a,n,u){switch(l){case"children":typeof a=="string"?t==="body"||t==="textarea"&&a===""||va(e,a):(typeof a=="number"||typeof a=="bigint")&&t!=="body"&&va(e,""+a);break;case"className":gi(e,"class",a);break;case"tabIndex":gi(e,"tabindex",a);break;case"dir":case"role":case"viewBox":case"width":case"height":gi(e,l,a);break;case"style":co(e,a,u);break;case"data":if(t!=="object"){gi(e,"data",a);break}case"src":case"href":if(a===""&&(t!=="a"||l!=="href")){e.removeAttribute(l);break}if(a==null||typeof a=="function"||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=bi(""+a),e.setAttribute(l,a);break;case"action":case"formAction":if(typeof a=="function"){e.setAttribute(l,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(l==="formAction"?(t!=="input"&&_e(e,t,"name",n.name,n,null),_e(e,t,"formEncType",n.formEncType,n,null),_e(e,t,"formMethod",n.formMethod,n,null),_e(e,t,"formTarget",n.formTarget,n,null)):(_e(e,t,"encType",n.encType,n,null),_e(e,t,"method",n.method,n,null),_e(e,t,"target",n.target,n,null)));if(a==null||typeof a=="symbol"||typeof a=="boolean"){e.removeAttribute(l);break}a=bi(""+a),e.setAttribute(l,a);break;case"onClick":a!=null&&(e.onclick=fu);break;case"onScroll":a!=null&&ge("scroll",e);break;case"onScrollEnd":a!=null&&ge("scrollend",e);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"multiple":e.multiple=a&&typeof a!="function"&&typeof a!="symbol";break;case"muted":e.muted=a&&typeof a!="function"&&typeof a!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(a==null||typeof a=="function"||typeof a=="boolean"||typeof a=="symbol"){e.removeAttribute("xlink:href");break}l=bi(""+a),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",l);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""+a):e.removeAttribute(l);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":a&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,""):e.removeAttribute(l);break;case"capture":case"download":a===!0?e.setAttribute(l,""):a!==!1&&a!=null&&typeof a!="function"&&typeof a!="symbol"?e.setAttribute(l,a):e.removeAttribute(l);break;case"cols":case"rows":case"size":case"span":a!=null&&typeof a!="function"&&typeof a!="symbol"&&!isNaN(a)&&1<=a?e.setAttribute(l,a):e.removeAttribute(l);break;case"rowSpan":case"start":a==null||typeof a=="function"||typeof a=="symbol"||isNaN(a)?e.removeAttribute(l):e.setAttribute(l,a);break;case"popover":ge("beforetoggle",e),ge("toggle",e),vi(e,"popover",a);break;case"xlinkActuate":Jt(e,"http://www.w3.org/1999/xlink","xlink:actuate",a);break;case"xlinkArcrole":Jt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",a);break;case"xlinkRole":Jt(e,"http://www.w3.org/1999/xlink","xlink:role",a);break;case"xlinkShow":Jt(e,"http://www.w3.org/1999/xlink","xlink:show",a);break;case"xlinkTitle":Jt(e,"http://www.w3.org/1999/xlink","xlink:title",a);break;case"xlinkType":Jt(e,"http://www.w3.org/1999/xlink","xlink:type",a);break;case"xmlBase":Jt(e,"http://www.w3.org/XML/1998/namespace","xml:base",a);break;case"xmlLang":Jt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",a);break;case"xmlSpace":Jt(e,"http://www.w3.org/XML/1998/namespace","xml:space",a);break;case"is":vi(e,"is",a);break;case"innerText":case"textContent":break;default:(!(2<l.length)||l[0]!=="o"&&l[0]!=="O"||l[1]!=="n"&&l[1]!=="N")&&(l=Tg.get(l)||l,vi(e,l,a))}}function Lr(e,t,l,a,n,u){switch(l){case"style":co(e,a,u);break;case"dangerouslySetInnerHTML":if(a!=null){if(typeof a!="object"||!("__html"in a))throw Error(c(61));if(l=a.__html,l!=null){if(n.children!=null)throw Error(c(60));e.innerHTML=l}}break;case"children":typeof a=="string"?va(e,a):(typeof a=="number"||typeof a=="bigint")&&va(e,""+a);break;case"onScroll":a!=null&&ge("scroll",e);break;case"onScrollEnd":a!=null&&ge("scrollend",e);break;case"onClick":a!=null&&(e.onclick=fu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Ws.hasOwnProperty(l))e:{if(l[0]==="o"&&l[1]==="n"&&(n=l.endsWith("Capture"),t=l.slice(2,n?l.length-7:void 0),u=e[st]||null,u=u!=null?u[l]:null,typeof u=="function"&&e.removeEventListener(t,u,n),typeof a=="function")){typeof u!="function"&&u!==null&&(l in e?e[l]=null:e.hasAttribute(l)&&e.removeAttribute(l)),e.addEventListener(t,a,n);break e}l in e?e[l]=a:a===!0?e.setAttribute(l,""):vi(e,l,a)}}}function tt(e,t,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":ge("error",e),ge("load",e);var a=!1,n=!1,u;for(u in l)if(l.hasOwnProperty(u)){var o=l[u];if(o!=null)switch(u){case"src":a=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:_e(e,t,u,o,l,null)}}n&&_e(e,t,"srcSet",l.srcSet,l,null),a&&_e(e,t,"src",l.src,l,null);return;case"input":ge("invalid",e);var m=u=o=n=null,p=null,M=null;for(a in l)if(l.hasOwnProperty(a)){var k=l[a];if(k!=null)switch(a){case"name":n=k;break;case"type":o=k;break;case"checked":p=k;break;case"defaultChecked":M=k;break;case"value":u=k;break;case"defaultValue":m=k;break;case"children":case"dangerouslySetInnerHTML":if(k!=null)throw Error(c(137,t));break;default:_e(e,t,a,k,l,null)}}ao(e,u,m,p,M,o,n,!1),yi(e);return;case"select":ge("invalid",e),a=o=u=null;for(n in l)if(l.hasOwnProperty(n)&&(m=l[n],m!=null))switch(n){case"value":u=m;break;case"defaultValue":o=m;break;case"multiple":a=m;default:_e(e,t,n,m,l,null)}t=u,l=o,e.multiple=!!a,t!=null?ha(e,!!a,t,!1):l!=null&&ha(e,!!a,l,!0);return;case"textarea":ge("invalid",e),u=n=a=null;for(o in l)if(l.hasOwnProperty(o)&&(m=l[o],m!=null))switch(o){case"value":a=m;break;case"defaultValue":n=m;break;case"children":u=m;break;case"dangerouslySetInnerHTML":if(m!=null)throw Error(c(91));break;default:_e(e,t,o,m,l,null)}io(e,a,n,u),yi(e);return;case"option":for(p in l)if(l.hasOwnProperty(p)&&(a=l[p],a!=null))switch(p){case"selected":e.selected=a&&typeof a!="function"&&typeof a!="symbol";break;default:_e(e,t,p,a,l,null)}return;case"dialog":ge("beforetoggle",e),ge("toggle",e),ge("cancel",e),ge("close",e);break;case"iframe":case"object":ge("load",e);break;case"video":case"audio":for(a=0;a<Kn.length;a++)ge(Kn[a],e);break;case"image":ge("error",e),ge("load",e);break;case"details":ge("toggle",e);break;case"embed":case"source":case"link":ge("error",e),ge("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(M in l)if(l.hasOwnProperty(M)&&(a=l[M],a!=null))switch(M){case"children":case"dangerouslySetInnerHTML":throw Error(c(137,t));default:_e(e,t,M,a,l,null)}return;default:if(tc(t)){for(k in l)l.hasOwnProperty(k)&&(a=l[k],a!==void 0&&Lr(e,t,k,a,l,void 0));return}}for(m in l)l.hasOwnProperty(m)&&(a=l[m],a!=null&&_e(e,t,m,a,l,null))}function $0(e,t,l,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,o=null,m=null,p=null,M=null,k=null;for(C in l){var q=l[C];if(l.hasOwnProperty(C)&&q!=null)switch(C){case"checked":break;case"value":break;case"defaultValue":p=q;default:a.hasOwnProperty(C)||_e(e,t,C,null,a,q)}}for(var w in a){var C=a[w];if(q=l[w],a.hasOwnProperty(w)&&(C!=null||q!=null))switch(w){case"type":u=C;break;case"name":n=C;break;case"checked":M=C;break;case"defaultChecked":k=C;break;case"value":o=C;break;case"defaultValue":m=C;break;case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(c(137,t));break;default:C!==q&&_e(e,t,w,C,a,q)}}Iu(e,o,m,p,M,k,u,n);return;case"select":C=o=m=w=null;for(u in l)if(p=l[u],l.hasOwnProperty(u)&&p!=null)switch(u){case"value":break;case"multiple":C=p;default:a.hasOwnProperty(u)||_e(e,t,u,null,a,p)}for(n in a)if(u=a[n],p=l[n],a.hasOwnProperty(n)&&(u!=null||p!=null))switch(n){case"value":w=u;break;case"defaultValue":m=u;break;case"multiple":o=u;default:u!==p&&_e(e,t,n,u,a,p)}t=m,l=o,a=C,w!=null?ha(e,!!l,w,!1):!!a!=!!l&&(t!=null?ha(e,!!l,t,!0):ha(e,!!l,l?[]:"",!1));return;case"textarea":C=w=null;for(m in l)if(n=l[m],l.hasOwnProperty(m)&&n!=null&&!a.hasOwnProperty(m))switch(m){case"value":break;case"children":break;default:_e(e,t,m,null,a,n)}for(o in a)if(n=a[o],u=l[o],a.hasOwnProperty(o)&&(n!=null||u!=null))switch(o){case"value":w=n;break;case"defaultValue":C=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(c(91));break;default:n!==u&&_e(e,t,o,n,a,u)}no(e,w,C);return;case"option":for(var ue in l)if(w=l[ue],l.hasOwnProperty(ue)&&w!=null&&!a.hasOwnProperty(ue))switch(ue){case"selected":e.selected=!1;break;default:_e(e,t,ue,null,a,w)}for(p in a)if(w=a[p],C=l[p],a.hasOwnProperty(p)&&w!==C&&(w!=null||C!=null))switch(p){case"selected":e.selected=w&&typeof w!="function"&&typeof w!="symbol";break;default:_e(e,t,p,w,a,C)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ae in l)w=l[ae],l.hasOwnProperty(ae)&&w!=null&&!a.hasOwnProperty(ae)&&_e(e,t,ae,null,a,w);for(M in a)if(w=a[M],C=l[M],a.hasOwnProperty(M)&&w!==C&&(w!=null||C!=null))switch(M){case"children":case"dangerouslySetInnerHTML":if(w!=null)throw Error(c(137,t));break;default:_e(e,t,M,w,a,C)}return;default:if(tc(t)){for(var Me in l)w=l[Me],l.hasOwnProperty(Me)&&w!==void 0&&!a.hasOwnProperty(Me)&&Lr(e,t,Me,void 0,a,w);for(k in a)w=a[k],C=l[k],!a.hasOwnProperty(k)||w===C||w===void 0&&C===void 0||Lr(e,t,k,w,a,C);return}}for(var A in l)w=l[A],l.hasOwnProperty(A)&&w!=null&&!a.hasOwnProperty(A)&&_e(e,t,A,null,a,w);for(q in a)w=a[q],C=l[q],!a.hasOwnProperty(q)||w===C||w==null&&C==null||_e(e,t,q,w,a,C)}var qr=null,Gr=null;function du(e){return e.nodeType===9?e:e.ownerDocument}function rm(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function sm(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Yr(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Vr=null;function W0(){var e=window.event;return e&&e.type==="popstate"?e===Vr?!1:(Vr=e,!0):(Vr=null,!1)}var om=typeof setTimeout=="function"?setTimeout:void 0,F0=typeof clearTimeout=="function"?clearTimeout:void 0,fm=typeof Promise=="function"?Promise:void 0,P0=typeof queueMicrotask=="function"?queueMicrotask:typeof fm<"u"?function(e){return fm.resolve(null).then(e).catch(I0)}:om;function I0(e){setTimeout(function(){throw e})}function Dl(e){return e==="head"}function dm(e,t){var l=t,a=0,n=0;do{var u=l.nextSibling;if(e.removeChild(l),u&&u.nodeType===8)if(l=u.data,l==="/$"){if(0<a&&8>a){l=a;var o=e.ownerDocument;if(l&1&&$n(o.documentElement),l&2&&$n(o.body),l&4)for(l=o.head,$n(l),o=l.firstChild;o;){var m=o.nextSibling,p=o.nodeName;o[on]||p==="SCRIPT"||p==="STYLE"||p==="LINK"&&o.rel.toLowerCase()==="stylesheet"||l.removeChild(o),o=m}}if(n===0){e.removeChild(u),ai(t);return}n--}else l==="$"||l==="$?"||l==="$!"?n++:a=l.charCodeAt(0)-48;else a=0;l=u}while(l);ai(t)}function Xr(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var l=t;switch(t=t.nextSibling,l.nodeName){case"HTML":case"HEAD":case"BODY":Xr(l),$u(l);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(l.rel.toLowerCase()==="stylesheet")continue}e.removeChild(l)}}function ey(e,t,l,a){for(;e.nodeType===1;){var n=l;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!a&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(a){if(!e[on])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=Ht(e.nextSibling),e===null)break}return null}function ty(e,t,l){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!l||(e=Ht(e.nextSibling),e===null))return null;return e}function Qr(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function ly(e,t){var l=e.ownerDocument;if(e.data!=="$?"||l.readyState==="complete")t();else{var a=function(){t(),l.removeEventListener("DOMContentLoaded",a)};l.addEventListener("DOMContentLoaded",a),e._reactRetry=a}}function Ht(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Zr=null;function mm(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var l=e.data;if(l==="$"||l==="$!"||l==="$?"){if(t===0)return e;t--}else l==="/$"&&t++}e=e.previousSibling}return null}function hm(e,t,l){switch(t=du(l),e){case"html":if(e=t.documentElement,!e)throw Error(c(452));return e;case"head":if(e=t.head,!e)throw Error(c(453));return e;case"body":if(e=t.body,!e)throw Error(c(454));return e;default:throw Error(c(451))}}function $n(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);$u(e)}var Ot=new Map,vm=new Set;function mu(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var rl=K.d;K.d={f:ay,r:ny,D:iy,C:uy,L:cy,m:ry,X:oy,S:sy,M:fy};function ay(){var e=rl.f(),t=nu();return e||t}function ny(e){var t=oa(e);t!==null&&t.tag===5&&t.type==="form"?Uf(t):rl.r(e)}var Xa=typeof document>"u"?null:document;function gm(e,t,l){var a=Xa;if(a&&typeof t=="string"&&t){var n=jt(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof l=="string"&&(n+='[crossorigin="'+l+'"]'),vm.has(n)||(vm.add(n),e={rel:e,crossOrigin:l,href:t},a.querySelector(n)===null&&(t=a.createElement("link"),tt(t,"link",e),$e(t),a.head.appendChild(t)))}}function iy(e){rl.D(e),gm("dns-prefetch",e,null)}function uy(e,t){rl.C(e,t),gm("preconnect",e,t)}function cy(e,t,l){rl.L(e,t,l);var a=Xa;if(a&&e&&t){var n='link[rel="preload"][as="'+jt(t)+'"]';t==="image"&&l&&l.imageSrcSet?(n+='[imagesrcset="'+jt(l.imageSrcSet)+'"]',typeof l.imageSizes=="string"&&(n+='[imagesizes="'+jt(l.imageSizes)+'"]')):n+='[href="'+jt(e)+'"]';var u=n;switch(t){case"style":u=Qa(e);break;case"script":u=Za(e)}Ot.has(u)||(e=E({rel:"preload",href:t==="image"&&l&&l.imageSrcSet?void 0:e,as:t},l),Ot.set(u,e),a.querySelector(n)!==null||t==="style"&&a.querySelector(Wn(u))||t==="script"&&a.querySelector(Fn(u))||(t=a.createElement("link"),tt(t,"link",e),$e(t),a.head.appendChild(t)))}}function ry(e,t){rl.m(e,t);var l=Xa;if(l&&e){var a=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+jt(a)+'"][href="'+jt(e)+'"]',u=n;switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Za(e)}if(!Ot.has(u)&&(e=E({rel:"modulepreload",href:e},t),Ot.set(u,e),l.querySelector(n)===null)){switch(a){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(l.querySelector(Fn(u)))return}a=l.createElement("link"),tt(a,"link",e),$e(a),l.head.appendChild(a)}}}function sy(e,t,l){rl.S(e,t,l);var a=Xa;if(a&&e){var n=fa(a).hoistableStyles,u=Qa(e);t=t||"default";var o=n.get(u);if(!o){var m={loading:0,preload:null};if(o=a.querySelector(Wn(u)))m.loading=5;else{e=E({rel:"stylesheet",href:e,"data-precedence":t},l),(l=Ot.get(u))&&Kr(e,l);var p=o=a.createElement("link");$e(p),tt(p,"link",e),p._p=new Promise(function(M,k){p.onload=M,p.onerror=k}),p.addEventListener("load",function(){m.loading|=1}),p.addEventListener("error",function(){m.loading|=2}),m.loading|=4,hu(o,t,a)}o={type:"stylesheet",instance:o,count:1,state:m},n.set(u,o)}}}function oy(e,t){rl.X(e,t);var l=Xa;if(l&&e){var a=fa(l).hoistableScripts,n=Za(e),u=a.get(n);u||(u=l.querySelector(Fn(n)),u||(e=E({src:e,async:!0},t),(t=Ot.get(n))&&Jr(e,t),u=l.createElement("script"),$e(u),tt(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function fy(e,t){rl.M(e,t);var l=Xa;if(l&&e){var a=fa(l).hoistableScripts,n=Za(e),u=a.get(n);u||(u=l.querySelector(Fn(n)),u||(e=E({src:e,async:!0,type:"module"},t),(t=Ot.get(n))&&Jr(e,t),u=l.createElement("script"),$e(u),tt(u,"link",e),l.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},a.set(n,u))}}function ym(e,t,l,a){var n=(n=ce.current)?mu(n):null;if(!n)throw Error(c(446));switch(e){case"meta":case"title":return null;case"style":return typeof l.precedence=="string"&&typeof l.href=="string"?(t=Qa(l.href),l=fa(n).hoistableStyles,a=l.get(t),a||(a={type:"style",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};case"link":if(l.rel==="stylesheet"&&typeof l.href=="string"&&typeof l.precedence=="string"){e=Qa(l.href);var u=fa(n).hoistableStyles,o=u.get(e);if(o||(n=n.ownerDocument||n,o={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,o),(u=n.querySelector(Wn(e)))&&!u._p&&(o.instance=u,o.state.loading=5),Ot.has(e)||(l={rel:"preload",as:"style",href:l.href,crossOrigin:l.crossOrigin,integrity:l.integrity,media:l.media,hrefLang:l.hrefLang,referrerPolicy:l.referrerPolicy},Ot.set(e,l),u||dy(n,e,l,o.state))),t&&a===null)throw Error(c(528,""));return o}if(t&&a!==null)throw Error(c(529,""));return null;case"script":return t=l.async,l=l.src,typeof l=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Za(l),l=fa(n).hoistableScripts,a=l.get(t),a||(a={type:"script",instance:null,count:0,state:null},l.set(t,a)),a):{type:"void",instance:null,count:0,state:null};default:throw Error(c(444,e))}}function Qa(e){return'href="'+jt(e)+'"'}function Wn(e){return'link[rel="stylesheet"]['+e+"]"}function pm(e){return E({},e,{"data-precedence":e.precedence,precedence:null})}function dy(e,t,l,a){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?a.loading=1:(t=e.createElement("link"),a.preload=t,t.addEventListener("load",function(){return a.loading|=1}),t.addEventListener("error",function(){return a.loading|=2}),tt(t,"link",l),$e(t),e.head.appendChild(t))}function Za(e){return'[src="'+jt(e)+'"]'}function Fn(e){return"script[async]"+e}function bm(e,t,l){if(t.count++,t.instance===null)switch(t.type){case"style":var a=e.querySelector('style[data-href~="'+jt(l.href)+'"]');if(a)return t.instance=a,$e(a),a;var n=E({},l,{"data-href":l.href,"data-precedence":l.precedence,href:null,precedence:null});return a=(e.ownerDocument||e).createElement("style"),$e(a),tt(a,"style",n),hu(a,l.precedence,e),t.instance=a;case"stylesheet":n=Qa(l.href);var u=e.querySelector(Wn(n));if(u)return t.state.loading|=4,t.instance=u,$e(u),u;a=pm(l),(n=Ot.get(n))&&Kr(a,n),u=(e.ownerDocument||e).createElement("link"),$e(u);var o=u;return o._p=new Promise(function(m,p){o.onload=m,o.onerror=p}),tt(u,"link",a),t.state.loading|=4,hu(u,l.precedence,e),t.instance=u;case"script":return u=Za(l.src),(n=e.querySelector(Fn(u)))?(t.instance=n,$e(n),n):(a=l,(n=Ot.get(u))&&(a=E({},l),Jr(a,n)),e=e.ownerDocument||e,n=e.createElement("script"),$e(n),tt(n,"link",a),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(c(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(a=t.instance,t.state.loading|=4,hu(a,l.precedence,e));return t.instance}function hu(e,t,l){for(var a=l.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=a.length?a[a.length-1]:null,u=n,o=0;o<a.length;o++){var m=a[o];if(m.dataset.precedence===t)u=m;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=l.nodeType===9?l.head:l,t.insertBefore(e,t.firstChild))}function Kr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Jr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var vu=null;function xm(e,t,l){if(vu===null){var a=new Map,n=vu=new Map;n.set(l,a)}else n=vu,a=n.get(l),a||(a=new Map,n.set(l,a));if(a.has(e))return a;for(a.set(e,null),l=l.getElementsByTagName(e),n=0;n<l.length;n++){var u=l[n];if(!(u[on]||u[at]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var o=u.getAttribute(t)||"";o=e+o;var m=a.get(o);m?m.push(u):a.set(o,[u])}}return a}function Sm(e,t,l){e=e.ownerDocument||e,e.head.insertBefore(l,t==="title"?e.querySelector("head > title"):null)}function my(e,t,l){if(l===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Em(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Pn=null;function hy(){}function vy(e,t,l){if(Pn===null)throw Error(c(475));var a=Pn;if(t.type==="stylesheet"&&(typeof l.media!="string"||matchMedia(l.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=Qa(l.href),u=e.querySelector(Wn(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(a.count++,a=gu.bind(a),e.then(a,a)),t.state.loading|=4,t.instance=u,$e(u);return}u=e.ownerDocument||e,l=pm(l),(n=Ot.get(n))&&Kr(l,n),u=u.createElement("link"),$e(u);var o=u;o._p=new Promise(function(m,p){o.onload=m,o.onerror=p}),tt(u,"link",l),t.instance=u}a.stylesheets===null&&(a.stylesheets=new Map),a.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(a.count++,t=gu.bind(a),e.addEventListener("load",t),e.addEventListener("error",t))}}function gy(){if(Pn===null)throw Error(c(475));var e=Pn;return e.stylesheets&&e.count===0&&$r(e,e.stylesheets),0<e.count?function(t){var l=setTimeout(function(){if(e.stylesheets&&$r(e,e.stylesheets),e.unsuspend){var a=e.unsuspend;e.unsuspend=null,a()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(l)}}:null}function gu(){if(this.count--,this.count===0){if(this.stylesheets)$r(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var yu=null;function $r(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,yu=new Map,t.forEach(yy,e),yu=null,gu.call(e))}function yy(e,t){if(!(t.state.loading&4)){var l=yu.get(e);if(l)var a=l.get(null);else{l=new Map,yu.set(e,l);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var o=n[u];(o.nodeName==="LINK"||o.getAttribute("media")!=="not all")&&(l.set(o.dataset.precedence,o),a=o)}a&&l.set(null,a)}n=t.instance,o=n.getAttribute("data-precedence"),u=l.get(o)||a,u===a&&l.set(null,n),l.set(o,n),this.count++,a=gu.bind(this),n.addEventListener("load",a),n.addEventListener("error",a),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var In={$$typeof:U,Provider:null,Consumer:null,_currentValue:G,_currentValue2:G,_threadCount:0};function py(e,t,l,a,n,u,o,m){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Qu(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Qu(0),this.hiddenUpdates=Qu(null),this.identifierPrefix=a,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=o,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=m,this.incompleteTransitions=new Map}function Nm(e,t,l,a,n,u,o,m,p,M,k,q){return e=new py(e,t,l,o,m,p,M,q),t=1,u===!0&&(t|=24),u=pt(3,null,null,t),e.current=u,u.stateNode=e,t=wc(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:a,isDehydrated:l,cache:t},zc(u),e}function Tm(e){return e?(e=Na,e):Na}function Am(e,t,l,a,n,u){n=Tm(n),a.context===null?a.context=n:a.pendingContext=n,a=bl(t),a.payload={element:l},u=u===void 0?null:u,u!==null&&(a.callback=u),l=xl(e,a,t),l!==null&&(Nt(l,e,t),Rn(l,e,t))}function jm(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var l=e.retryLane;e.retryLane=l!==0&&l<t?l:t}}function Wr(e,t){jm(e,t),(e=e.alternate)&&jm(e,t)}function _m(e){if(e.tag===13){var t=Ea(e,67108864);t!==null&&Nt(t,e,67108864),Wr(e,67108864)}}var pu=!0;function by(e,t,l,a){var n=D.T;D.T=null;var u=K.p;try{K.p=2,Fr(e,t,l,a)}finally{K.p=u,D.T=n}}function xy(e,t,l,a){var n=D.T;D.T=null;var u=K.p;try{K.p=8,Fr(e,t,l,a)}finally{K.p=u,D.T=n}}function Fr(e,t,l,a){if(pu){var n=Pr(a);if(n===null)Br(e,t,a,bu,l),Rm(e,a);else if(Ey(n,e,t,l,a))a.stopPropagation();else if(Rm(e,a),t&4&&-1<Sy.indexOf(e)){for(;n!==null;){var u=oa(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var o=Yl(u.pendingLanes);if(o!==0){var m=u;for(m.pendingLanes|=2,m.entangledLanes|=2;o;){var p=1<<31-gt(o);m.entanglements[1]|=p,o&=~p}Qt(u),(Te&6)===0&&(lu=qt()+500,Zn(0))}}break;case 13:m=Ea(u,2),m!==null&&Nt(m,u,2),nu(),Wr(u,2)}if(u=Pr(a),u===null&&Br(e,t,a,bu,l),u===n)break;n=u}n!==null&&a.stopPropagation()}else Br(e,t,a,null,l)}}function Pr(e){return e=ac(e),Ir(e)}var bu=null;function Ir(e){if(bu=null,e=sa(e),e!==null){var t=h(e);if(t===null)e=null;else{var l=t.tag;if(l===13){if(e=v(t),e!==null)return e;e=null}else if(l===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return bu=e,null}function Mm(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ug()){case qs:return 2;case Gs:return 8;case fi:case cg:return 32;case Ys:return 268435456;default:return 32}default:return 32}}var es=!1,Ol=null,zl=null,Ul=null,ei=new Map,ti=new Map,Hl=[],Sy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Rm(e,t){switch(e){case"focusin":case"focusout":Ol=null;break;case"dragenter":case"dragleave":zl=null;break;case"mouseover":case"mouseout":Ul=null;break;case"pointerover":case"pointerout":ei.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ti.delete(t.pointerId)}}function li(e,t,l,a,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:l,eventSystemFlags:a,nativeEvent:u,targetContainers:[n]},t!==null&&(t=oa(t),t!==null&&_m(t)),e):(e.eventSystemFlags|=a,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Ey(e,t,l,a,n){switch(t){case"focusin":return Ol=li(Ol,e,t,l,a,n),!0;case"dragenter":return zl=li(zl,e,t,l,a,n),!0;case"mouseover":return Ul=li(Ul,e,t,l,a,n),!0;case"pointerover":var u=n.pointerId;return ei.set(u,li(ei.get(u)||null,e,t,l,a,n)),!0;case"gotpointercapture":return u=n.pointerId,ti.set(u,li(ti.get(u)||null,e,t,l,a,n)),!0}return!1}function wm(e){var t=sa(e.target);if(t!==null){var l=h(t);if(l!==null){if(t=l.tag,t===13){if(t=v(l),t!==null){e.blockedOn=t,vg(e.priority,function(){if(l.tag===13){var a=Et();a=Zu(a);var n=Ea(l,a);n!==null&&Nt(n,l,a),Wr(l,a)}});return}}else if(t===3&&l.stateNode.current.memoizedState.isDehydrated){e.blockedOn=l.tag===3?l.stateNode.containerInfo:null;return}}}e.blockedOn=null}function xu(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var l=Pr(e.nativeEvent);if(l===null){l=e.nativeEvent;var a=new l.constructor(l.type,l);lc=a,l.target.dispatchEvent(a),lc=null}else return t=oa(l),t!==null&&_m(t),e.blockedOn=l,!1;t.shift()}return!0}function Cm(e,t,l){xu(e)&&l.delete(t)}function Ny(){es=!1,Ol!==null&&xu(Ol)&&(Ol=null),zl!==null&&xu(zl)&&(zl=null),Ul!==null&&xu(Ul)&&(Ul=null),ei.forEach(Cm),ti.forEach(Cm)}function Su(e,t){e.blockedOn===t&&(e.blockedOn=null,es||(es=!0,i.unstable_scheduleCallback(i.unstable_NormalPriority,Ny)))}var Eu=null;function Dm(e){Eu!==e&&(Eu=e,i.unstable_scheduleCallback(i.unstable_NormalPriority,function(){Eu===e&&(Eu=null);for(var t=0;t<e.length;t+=3){var l=e[t],a=e[t+1],n=e[t+2];if(typeof a!="function"){if(Ir(a||l)===null)continue;break}var u=oa(l);u!==null&&(e.splice(t,3),t-=3,Ic(u,{pending:!0,data:n,method:l.method,action:a},a,n))}}))}function ai(e){function t(p){return Su(p,e)}Ol!==null&&Su(Ol,e),zl!==null&&Su(zl,e),Ul!==null&&Su(Ul,e),ei.forEach(t),ti.forEach(t);for(var l=0;l<Hl.length;l++){var a=Hl[l];a.blockedOn===e&&(a.blockedOn=null)}for(;0<Hl.length&&(l=Hl[0],l.blockedOn===null);)wm(l),l.blockedOn===null&&Hl.shift();if(l=(e.ownerDocument||e).$$reactFormReplay,l!=null)for(a=0;a<l.length;a+=3){var n=l[a],u=l[a+1],o=n[st]||null;if(typeof u=="function")o||Dm(l);else if(o){var m=null;if(u&&u.hasAttribute("formAction")){if(n=u,o=u[st]||null)m=o.formAction;else if(Ir(n)!==null)continue}else m=o.action;typeof m=="function"?l[a+1]=m:(l.splice(a,3),a-=3),Dm(l)}}}function ts(e){this._internalRoot=e}Nu.prototype.render=ts.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(c(409));var l=t.current,a=Et();Am(l,a,e,t,null,null)},Nu.prototype.unmount=ts.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Am(e.current,2,null,e,null,null),nu(),t[ra]=null}};function Nu(e){this._internalRoot=e}Nu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Ks();e={blockedOn:null,target:e,priority:t};for(var l=0;l<Hl.length&&t!==0&&t<Hl[l].priority;l++);Hl.splice(l,0,e),l===0&&wm(e)}};var Om=r.version;if(Om!=="19.1.0")throw Error(c(527,Om,"19.1.0"));K.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(c(188)):(e=Object.keys(e).join(","),Error(c(268,e)));return e=x(t),e=e!==null?y(e):null,e=e===null?null:e.stateNode,e};var Ty={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:D,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Tu=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Tu.isDisabled&&Tu.supportsFiber)try{cn=Tu.inject(Ty),vt=Tu}catch{}}return ii.createRoot=function(e,t){if(!d(e))throw Error(c(299));var l=!1,a="",n=$f,u=Wf,o=Ff,m=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(a=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(o=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(m=t.unstable_transitionCallbacks)),t=Nm(e,1,!1,null,null,l,a,n,u,o,m,null),e[ra]=t.current,kr(e),new ts(t)},ii.hydrateRoot=function(e,t,l){if(!d(e))throw Error(c(299));var a=!1,n="",u=$f,o=Wf,m=Ff,p=null,M=null;return l!=null&&(l.unstable_strictMode===!0&&(a=!0),l.identifierPrefix!==void 0&&(n=l.identifierPrefix),l.onUncaughtError!==void 0&&(u=l.onUncaughtError),l.onCaughtError!==void 0&&(o=l.onCaughtError),l.onRecoverableError!==void 0&&(m=l.onRecoverableError),l.unstable_transitionCallbacks!==void 0&&(p=l.unstable_transitionCallbacks),l.formState!==void 0&&(M=l.formState)),t=Nm(e,1,!0,t,l??null,a,n,u,o,m,p,M),t.context=Tm(null),l=t.current,a=Et(),a=Zu(a),n=bl(a),n.callback=null,xl(l,n,a),l=a,t.current.lanes=l,sn(t,l),Qt(t),e[ra]=t.current,kr(e),new Nu(t)},ii.version="19.1.0",ii}var Vm;function zy(){if(Vm)return ns.exports;Vm=1;function i(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(i)}catch(r){console.error(r)}}return i(),ns.exports=Oy(),ns.exports}var Uy=zy();function De(i,r,{checkForDefaultPrevented:s=!0}={}){return function(d){if(i==null||i(d),s===!1||!d.defaultPrevented)return r==null?void 0:r(d)}}function Hy(i,r){const s=b.createContext(r),c=h=>{const{children:v,...g}=h,x=b.useMemo(()=>g,Object.values(g));return f.jsx(s.Provider,{value:x,children:v})};c.displayName=i+"Provider";function d(h){const v=b.useContext(s);if(v)return v;if(r!==void 0)return r;throw new Error(`\`${h}\` must be used within \`${i}\``)}return[c,d]}function tn(i,r=[]){let s=[];function c(h,v){const g=b.createContext(v),x=s.length;s=[...s,v];const y=T=>{var V;const{scope:R,children:O,...H}=T,j=((V=R==null?void 0:R[i])==null?void 0:V[x])||g,z=b.useMemo(()=>H,Object.values(H));return f.jsx(j.Provider,{value:z,children:O})};y.displayName=h+"Provider";function E(T,R){var j;const O=((j=R==null?void 0:R[i])==null?void 0:j[x])||g,H=b.useContext(O);if(H)return H;if(v!==void 0)return v;throw new Error(`\`${T}\` must be used within \`${h}\``)}return[y,E]}const d=()=>{const h=s.map(v=>b.createContext(v));return function(g){const x=(g==null?void 0:g[i])||h;return b.useMemo(()=>({[`__scope${i}`]:{...g,[i]:x}}),[g,x])}};return d.scopeName=i,[c,ky(d,...r)]}function ky(...i){const r=i[0];if(i.length===1)return r;const s=()=>{const c=i.map(d=>({useScope:d(),scopeName:d.scopeName}));return function(h){const v=c.reduce((g,{useScope:x,scopeName:y})=>{const T=x(h)[`__scope${y}`];return{...g,...T}},{});return b.useMemo(()=>({[`__scope${r.scopeName}`]:v}),[v])}};return s.scopeName=r.scopeName,s}function Xm(i,r){if(typeof i=="function")return i(r);i!=null&&(i.current=r)}function By(...i){return r=>{let s=!1;const c=i.map(d=>{const h=Xm(d,r);return!s&&typeof h=="function"&&(s=!0),h});if(s)return()=>{for(let d=0;d<c.length;d++){const h=c[d];typeof h=="function"?h():Xm(i[d],null)}}}}function Ke(...i){return b.useCallback(By(...i),i)}function ri(i){const r=Ly(i),s=b.forwardRef((c,d)=>{const{children:h,...v}=c,g=b.Children.toArray(h),x=g.find(Gy);if(x){const y=x.props.children,E=g.map(T=>T===x?b.Children.count(y)>1?b.Children.only(null):b.isValidElement(y)?y.props.children:null:T);return f.jsx(r,{...v,ref:d,children:b.isValidElement(y)?b.cloneElement(y,void 0,E):null})}return f.jsx(r,{...v,ref:d,children:h})});return s.displayName=`${i}.Slot`,s}var yh=ri("Slot");function Ly(i){const r=b.forwardRef((s,c)=>{const{children:d,...h}=s,v=b.isValidElement(d)?Vy(d):void 0,g=Ke(v,c);if(b.isValidElement(d)){const x=Yy(h,d.props);return d.type!==b.Fragment&&(x.ref=g),b.cloneElement(d,x)}return b.Children.count(d)>1?b.Children.only(null):null});return r.displayName=`${i}.SlotClone`,r}var qy=Symbol("radix.slottable");function Gy(i){return b.isValidElement(i)&&typeof i.type=="function"&&"__radixId"in i.type&&i.type.__radixId===qy}function Yy(i,r){const s={...r};for(const c in r){const d=i[c],h=r[c];/^on[A-Z]/.test(c)?d&&h?s[c]=(...g)=>{const x=h(...g);return d(...g),x}:d&&(s[c]=d):c==="style"?s[c]={...d,...h}:c==="className"&&(s[c]=[d,h].filter(Boolean).join(" "))}return{...i,...s}}function Vy(i){var c,d;let r=(c=Object.getOwnPropertyDescriptor(i.props,"ref"))==null?void 0:c.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?i.ref:(r=(d=Object.getOwnPropertyDescriptor(i,"ref"))==null?void 0:d.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?i.props.ref:i.props.ref||i.ref)}function ph(i){const r=i+"CollectionProvider",[s,c]=tn(r),[d,h]=s(r,{collectionRef:{current:null},itemMap:new Map}),v=j=>{const{scope:z,children:V}=j,J=Ll.useRef(null),U=Ll.useRef(new Map).current;return f.jsx(d,{scope:z,itemMap:U,collectionRef:J,children:V})};v.displayName=r;const g=i+"CollectionSlot",x=ri(g),y=Ll.forwardRef((j,z)=>{const{scope:V,children:J}=j,U=h(g,V),X=Ke(z,U.collectionRef);return f.jsx(x,{ref:X,children:J})});y.displayName=g;const E=i+"CollectionItemSlot",T="data-radix-collection-item",R=ri(E),O=Ll.forwardRef((j,z)=>{const{scope:V,children:J,...U}=j,X=Ll.useRef(null),Z=Ke(z,X),le=h(E,V);return Ll.useEffect(()=>(le.itemMap.set(X,{ref:X,...U}),()=>void le.itemMap.delete(X))),f.jsx(R,{[T]:"",ref:Z,children:J})});O.displayName=E;function H(j){const z=h(i+"CollectionConsumer",j);return Ll.useCallback(()=>{const J=z.collectionRef.current;if(!J)return[];const U=Array.from(J.querySelectorAll(`[${T}]`));return Array.from(z.itemMap.values()).sort((le,F)=>U.indexOf(le.ref.current)-U.indexOf(F.ref.current))},[z.collectionRef,z.itemMap])}return[{Provider:v,Slot:y,ItemSlot:O},H,c]}var Pa=globalThis!=null&&globalThis.document?b.useLayoutEffect:()=>{},Xy=vh[" useId ".trim().toString()]||(()=>{}),Qy=0;function ci(i){const[r,s]=b.useState(Xy());return Pa(()=>{s(c=>c??String(Qy++))},[i]),i||(r?`radix-${r}`:"")}var bh=gh();const Zy=hh(bh);var Ky=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"],ze=Ky.reduce((i,r)=>{const s=ri(`Primitive.${r}`),c=b.forwardRef((d,h)=>{const{asChild:v,...g}=d,x=v?s:r;return typeof window<"u"&&(window[Symbol.for("radix-ui")]=!0),f.jsx(x,{...g,ref:h})});return c.displayName=`Primitive.${r}`,{...i,[r]:c}},{});function Jy(i,r){i&&bh.flushSync(()=>i.dispatchEvent(r))}function Ia(i){const r=b.useRef(i);return b.useEffect(()=>{r.current=i}),b.useMemo(()=>(...s)=>{var c;return(c=r.current)==null?void 0:c.call(r,...s)},[])}var $y=vh[" useInsertionEffect ".trim().toString()]||Pa;function si({prop:i,defaultProp:r,onChange:s=()=>{},caller:c}){const[d,h,v]=Wy({defaultProp:r,onChange:s}),g=i!==void 0,x=g?i:d;{const E=b.useRef(i!==void 0);b.useEffect(()=>{const T=E.current;T!==g&&console.warn(`${c} is changing from ${T?"controlled":"uncontrolled"} to ${g?"controlled":"uncontrolled"}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`),E.current=g},[g,c])}const y=b.useCallback(E=>{var T;if(g){const R=Fy(E)?E(i):E;R!==i&&((T=v.current)==null||T.call(v,R))}else h(E)},[g,i,h,v]);return[x,y]}function Wy({defaultProp:i,onChange:r}){const[s,c]=b.useState(i),d=b.useRef(s),h=b.useRef(r);return $y(()=>{h.current=r},[r]),b.useEffect(()=>{var v;d.current!==s&&((v=h.current)==null||v.call(h,s),d.current=s)},[s,d]),[s,c,h]}function Fy(i){return typeof i=="function"}var Py=b.createContext(void 0);function Ds(i){const r=b.useContext(Py);return i||r||"ltr"}var rs="rovingFocusGroup.onEntryFocus",Iy={bubbles:!1,cancelable:!0},oi="RovingFocusGroup",[bs,xh,ep]=ph(oi),[tp,Sh]=tn(oi,[ep]),[lp,ap]=tp(oi),Eh=b.forwardRef((i,r)=>f.jsx(bs.Provider,{scope:i.__scopeRovingFocusGroup,children:f.jsx(bs.Slot,{scope:i.__scopeRovingFocusGroup,children:f.jsx(np,{...i,ref:r})})}));Eh.displayName=oi;var np=b.forwardRef((i,r)=>{const{__scopeRovingFocusGroup:s,orientation:c,loop:d=!1,dir:h,currentTabStopId:v,defaultCurrentTabStopId:g,onCurrentTabStopIdChange:x,onEntryFocus:y,preventScrollOnEntryFocus:E=!1,...T}=i,R=b.useRef(null),O=Ke(r,R),H=Ds(h),[j,z]=si({prop:v,defaultProp:g??null,onChange:x,caller:oi}),[V,J]=b.useState(!1),U=Ia(y),X=xh(s),Z=b.useRef(!1),[le,F]=b.useState(0);return b.useEffect(()=>{const L=R.current;if(L)return L.addEventListener(rs,U),()=>L.removeEventListener(rs,U)},[U]),f.jsx(lp,{scope:s,orientation:c,dir:H,loop:d,currentTabStopId:j,onItemFocus:b.useCallback(L=>z(L),[z]),onItemShiftTab:b.useCallback(()=>J(!0),[]),onFocusableItemAdd:b.useCallback(()=>F(L=>L+1),[]),onFocusableItemRemove:b.useCallback(()=>F(L=>L-1),[]),children:f.jsx(ze.div,{tabIndex:V||le===0?-1:0,"data-orientation":c,...T,ref:O,style:{outline:"none",...i.style},onMouseDown:De(i.onMouseDown,()=>{Z.current=!0}),onFocus:De(i.onFocus,L=>{const ne=!Z.current;if(L.target===L.currentTarget&&ne&&!V){const re=new CustomEvent(rs,Iy);if(L.currentTarget.dispatchEvent(re),!re.defaultPrevented){const se=X().filter(D=>D.focusable),pe=se.find(D=>D.active),Ve=se.find(D=>D.id===j),Ne=[pe,Ve,...se].filter(Boolean).map(D=>D.ref.current);Ah(Ne,E)}}Z.current=!1}),onBlur:De(i.onBlur,()=>J(!1))})})}),Nh="RovingFocusGroupItem",Th=b.forwardRef((i,r)=>{const{__scopeRovingFocusGroup:s,focusable:c=!0,active:d=!1,tabStopId:h,children:v,...g}=i,x=ci(),y=h||x,E=ap(Nh,s),T=E.currentTabStopId===y,R=xh(s),{onFocusableItemAdd:O,onFocusableItemRemove:H,currentTabStopId:j}=E;return b.useEffect(()=>{if(c)return O(),()=>H()},[c,O,H]),f.jsx(bs.ItemSlot,{scope:s,id:y,focusable:c,active:d,children:f.jsx(ze.span,{tabIndex:T?0:-1,"data-orientation":E.orientation,...g,ref:r,onMouseDown:De(i.onMouseDown,z=>{c?E.onItemFocus(y):z.preventDefault()}),onFocus:De(i.onFocus,()=>E.onItemFocus(y)),onKeyDown:De(i.onKeyDown,z=>{if(z.key==="Tab"&&z.shiftKey){E.onItemShiftTab();return}if(z.target!==z.currentTarget)return;const V=cp(z,E.orientation,E.dir);if(V!==void 0){if(z.metaKey||z.ctrlKey||z.altKey||z.shiftKey)return;z.preventDefault();let U=R().filter(X=>X.focusable).map(X=>X.ref.current);if(V==="last")U.reverse();else if(V==="prev"||V==="next"){V==="prev"&&U.reverse();const X=U.indexOf(z.currentTarget);U=E.loop?rp(U,X+1):U.slice(X+1)}setTimeout(()=>Ah(U))}}),children:typeof v=="function"?v({isCurrentTabStop:T,hasTabStop:j!=null}):v})})});Th.displayName=Nh;var ip={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function up(i,r){return r!=="rtl"?i:i==="ArrowLeft"?"ArrowRight":i==="ArrowRight"?"ArrowLeft":i}function cp(i,r,s){const c=up(i.key,s);if(!(r==="vertical"&&["ArrowLeft","ArrowRight"].includes(c))&&!(r==="horizontal"&&["ArrowUp","ArrowDown"].includes(c)))return ip[c]}function Ah(i,r=!1){const s=document.activeElement;for(const c of i)if(c===s||(c.focus({preventScroll:r}),document.activeElement!==s))return}function rp(i,r){return i.map((s,c)=>i[(r+c)%i.length])}var sp=Eh,op=Th;function fp(i,r){return b.useReducer((s,c)=>r[s][c]??s,i)}var ln=i=>{const{present:r,children:s}=i,c=dp(r),d=typeof s=="function"?s({present:c.isPresent}):b.Children.only(s),h=Ke(c.ref,mp(d));return typeof s=="function"||c.isPresent?b.cloneElement(d,{ref:h}):null};ln.displayName="Presence";function dp(i){const[r,s]=b.useState(),c=b.useRef(null),d=b.useRef(i),h=b.useRef("none"),v=i?"mounted":"unmounted",[g,x]=fp(v,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return b.useEffect(()=>{const y=Au(c.current);h.current=g==="mounted"?y:"none"},[g]),Pa(()=>{const y=c.current,E=d.current;if(E!==i){const R=h.current,O=Au(y);i?x("MOUNT"):O==="none"||(y==null?void 0:y.display)==="none"?x("UNMOUNT"):x(E&&R!==O?"ANIMATION_OUT":"UNMOUNT"),d.current=i}},[i,x]),Pa(()=>{if(r){let y;const E=r.ownerDocument.defaultView??window,T=O=>{const j=Au(c.current).includes(O.animationName);if(O.target===r&&j&&(x("ANIMATION_END"),!d.current)){const z=r.style.animationFillMode;r.style.animationFillMode="forwards",y=E.setTimeout(()=>{r.style.animationFillMode==="forwards"&&(r.style.animationFillMode=z)})}},R=O=>{O.target===r&&(h.current=Au(c.current))};return r.addEventListener("animationstart",R),r.addEventListener("animationcancel",T),r.addEventListener("animationend",T),()=>{E.clearTimeout(y),r.removeEventListener("animationstart",R),r.removeEventListener("animationcancel",T),r.removeEventListener("animationend",T)}}else x("ANIMATION_END")},[r,x]),{isPresent:["mounted","unmountSuspended"].includes(g),ref:b.useCallback(y=>{c.current=y?getComputedStyle(y):null,s(y)},[])}}function Au(i){return(i==null?void 0:i.animationName)||"none"}function mp(i){var c,d;let r=(c=Object.getOwnPropertyDescriptor(i.props,"ref"))==null?void 0:c.get,s=r&&"isReactWarning"in r&&r.isReactWarning;return s?i.ref:(r=(d=Object.getOwnPropertyDescriptor(i,"ref"))==null?void 0:d.get,s=r&&"isReactWarning"in r&&r.isReactWarning,s?i.props.ref:i.props.ref||i.ref)}var ku="Tabs",[hp,Gx]=tn(ku,[Sh]),jh=Sh(),[vp,Os]=hp(ku),_h=b.forwardRef((i,r)=>{const{__scopeTabs:s,value:c,onValueChange:d,defaultValue:h,orientation:v="horizontal",dir:g,activationMode:x="automatic",...y}=i,E=Ds(g),[T,R]=si({prop:c,onChange:d,defaultProp:h??"",caller:ku});return f.jsx(vp,{scope:s,baseId:ci(),value:T,onValueChange:R,orientation:v,dir:E,activationMode:x,children:f.jsx(ze.div,{dir:E,"data-orientation":v,...y,ref:r})})});_h.displayName=ku;var Mh="TabsList",Rh=b.forwardRef((i,r)=>{const{__scopeTabs:s,loop:c=!0,...d}=i,h=Os(Mh,s),v=jh(s);return f.jsx(sp,{asChild:!0,...v,orientation:h.orientation,dir:h.dir,loop:c,children:f.jsx(ze.div,{role:"tablist","aria-orientation":h.orientation,...d,ref:r})})});Rh.displayName=Mh;var wh="TabsTrigger",Ch=b.forwardRef((i,r)=>{const{__scopeTabs:s,value:c,disabled:d=!1,...h}=i,v=Os(wh,s),g=jh(s),x=zh(v.baseId,c),y=Uh(v.baseId,c),E=c===v.value;return f.jsx(op,{asChild:!0,...g,focusable:!d,active:E,children:f.jsx(ze.button,{type:"button",role:"tab","aria-selected":E,"aria-controls":y,"data-state":E?"active":"inactive","data-disabled":d?"":void 0,disabled:d,id:x,...h,ref:r,onMouseDown:De(i.onMouseDown,T=>{!d&&T.button===0&&T.ctrlKey===!1?v.onValueChange(c):T.preventDefault()}),onKeyDown:De(i.onKeyDown,T=>{[" ","Enter"].includes(T.key)&&v.onValueChange(c)}),onFocus:De(i.onFocus,()=>{const T=v.activationMode!=="manual";!E&&!d&&T&&v.onValueChange(c)})})})});Ch.displayName=wh;var Dh="TabsContent",Oh=b.forwardRef((i,r)=>{const{__scopeTabs:s,value:c,forceMount:d,children:h,...v}=i,g=Os(Dh,s),x=zh(g.baseId,c),y=Uh(g.baseId,c),E=c===g.value,T=b.useRef(E);return b.useEffect(()=>{const R=requestAnimationFrame(()=>T.current=!1);return()=>cancelAnimationFrame(R)},[]),f.jsx(ln,{present:d||E,children:({present:R})=>f.jsx(ze.div,{"data-state":E?"active":"inactive","data-orientation":g.orientation,role:"tabpanel","aria-labelledby":x,hidden:!R,id:y,tabIndex:0,...v,ref:r,style:{...i.style,animationDuration:T.current?"0s":void 0},children:R&&h})})});Oh.displayName=Dh;function zh(i,r){return`${i}-trigger-${r}`}function Uh(i,r){return`${i}-content-${r}`}var gp=_h,yp=Rh,pp=Ch,bp=Oh;function Hh(i){var r,s,c="";if(typeof i=="string"||typeof i=="number")c+=i;else if(typeof i=="object")if(Array.isArray(i)){var d=i.length;for(r=0;r<d;r++)i[r]&&(s=Hh(i[r]))&&(c&&(c+=" "),c+=s)}else for(s in i)i[s]&&(c&&(c+=" "),c+=s);return c}function kh(){for(var i,r,s=0,c="",d=arguments.length;s<d;s++)(i=arguments[s])&&(r=Hh(i))&&(c&&(c+=" "),c+=r);return c}const zs="-",xp=i=>{const r=Ep(i),{conflictingClassGroups:s,conflictingClassGroupModifiers:c}=i;return{getClassGroupId:v=>{const g=v.split(zs);return g[0]===""&&g.length!==1&&g.shift(),Bh(g,r)||Sp(v)},getConflictingClassGroupIds:(v,g)=>{const x=s[v]||[];return g&&c[v]?[...x,...c[v]]:x}}},Bh=(i,r)=>{var v;if(i.length===0)return r.classGroupId;const s=i[0],c=r.nextPart.get(s),d=c?Bh(i.slice(1),c):void 0;if(d)return d;if(r.validators.length===0)return;const h=i.join(zs);return(v=r.validators.find(({validator:g})=>g(h)))==null?void 0:v.classGroupId},Qm=/^\[(.+)\]$/,Sp=i=>{if(Qm.test(i)){const r=Qm.exec(i)[1],s=r==null?void 0:r.substring(0,r.indexOf(":"));if(s)return"arbitrary.."+s}},Ep=i=>{const{theme:r,classGroups:s}=i,c={nextPart:new Map,validators:[]};for(const d in s)xs(s[d],c,d,r);return c},xs=(i,r,s,c)=>{i.forEach(d=>{if(typeof d=="string"){const h=d===""?r:Zm(r,d);h.classGroupId=s;return}if(typeof d=="function"){if(Np(d)){xs(d(c),r,s,c);return}r.validators.push({validator:d,classGroupId:s});return}Object.entries(d).forEach(([h,v])=>{xs(v,Zm(r,h),s,c)})})},Zm=(i,r)=>{let s=i;return r.split(zs).forEach(c=>{s.nextPart.has(c)||s.nextPart.set(c,{nextPart:new Map,validators:[]}),s=s.nextPart.get(c)}),s},Np=i=>i.isThemeGetter,Tp=i=>{if(i<1)return{get:()=>{},set:()=>{}};let r=0,s=new Map,c=new Map;const d=(h,v)=>{s.set(h,v),r++,r>i&&(r=0,c=s,s=new Map)};return{get(h){let v=s.get(h);if(v!==void 0)return v;if((v=c.get(h))!==void 0)return d(h,v),v},set(h,v){s.has(h)?s.set(h,v):d(h,v)}}},Ss="!",Es=":",Ap=Es.length,jp=i=>{const{prefix:r,experimentalParseClassName:s}=i;let c=d=>{const h=[];let v=0,g=0,x=0,y;for(let H=0;H<d.length;H++){let j=d[H];if(v===0&&g===0){if(j===Es){h.push(d.slice(x,H)),x=H+Ap;continue}if(j==="/"){y=H;continue}}j==="["?v++:j==="]"?v--:j==="("?g++:j===")"&&g--}const E=h.length===0?d:d.substring(x),T=_p(E),R=T!==E,O=y&&y>x?y-x:void 0;return{modifiers:h,hasImportantModifier:R,baseClassName:T,maybePostfixModifierPosition:O}};if(r){const d=r+Es,h=c;c=v=>v.startsWith(d)?h(v.substring(d.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:v,maybePostfixModifierPosition:void 0}}if(s){const d=c;c=h=>s({className:h,parseClassName:d})}return c},_p=i=>i.endsWith(Ss)?i.substring(0,i.length-1):i.startsWith(Ss)?i.substring(1):i,Mp=i=>{const r=Object.fromEntries(i.orderSensitiveModifiers.map(c=>[c,!0]));return c=>{if(c.length<=1)return c;const d=[];let h=[];return c.forEach(v=>{v[0]==="["||r[v]?(d.push(...h.sort(),v),h=[]):h.push(v)}),d.push(...h.sort()),d}},Rp=i=>({cache:Tp(i.cacheSize),parseClassName:jp(i),sortModifiers:Mp(i),...xp(i)}),wp=/\s+/,Cp=(i,r)=>{const{parseClassName:s,getClassGroupId:c,getConflictingClassGroupIds:d,sortModifiers:h}=r,v=[],g=i.trim().split(wp);let x="";for(let y=g.length-1;y>=0;y-=1){const E=g[y],{isExternal:T,modifiers:R,hasImportantModifier:O,baseClassName:H,maybePostfixModifierPosition:j}=s(E);if(T){x=E+(x.length>0?" "+x:x);continue}let z=!!j,V=c(z?H.substring(0,j):H);if(!V){if(!z){x=E+(x.length>0?" "+x:x);continue}if(V=c(H),!V){x=E+(x.length>0?" "+x:x);continue}z=!1}const J=h(R).join(":"),U=O?J+Ss:J,X=U+V;if(v.includes(X))continue;v.push(X);const Z=d(V,z);for(let le=0;le<Z.length;++le){const F=Z[le];v.push(U+F)}x=E+(x.length>0?" "+x:x)}return x};function Dp(){let i=0,r,s,c="";for(;i<arguments.length;)(r=arguments[i++])&&(s=Lh(r))&&(c&&(c+=" "),c+=s);return c}const Lh=i=>{if(typeof i=="string")return i;let r,s="";for(let c=0;c<i.length;c++)i[c]&&(r=Lh(i[c]))&&(s&&(s+=" "),s+=r);return s};function Op(i,...r){let s,c,d,h=v;function v(x){const y=r.reduce((E,T)=>T(E),i());return s=Rp(y),c=s.cache.get,d=s.cache.set,h=g,g(x)}function g(x){const y=c(x);if(y)return y;const E=Cp(x,s);return d(x,E),E}return function(){return h(Dp.apply(null,arguments))}}const Ze=i=>{const r=s=>s[i]||[];return r.isThemeGetter=!0,r},qh=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Gh=/^\((?:(\w[\w-]*):)?(.+)\)$/i,zp=/^\d+\/\d+$/,Up=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Hp=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,kp=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Bp=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,Lp=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,Ka=i=>zp.test(i),de=i=>!!i&&!Number.isNaN(Number(i)),Bl=i=>!!i&&Number.isInteger(Number(i)),ss=i=>i.endsWith("%")&&de(i.slice(0,-1)),sl=i=>Up.test(i),qp=()=>!0,Gp=i=>Hp.test(i)&&!kp.test(i),Yh=()=>!1,Yp=i=>Bp.test(i),Vp=i=>Lp.test(i),Xp=i=>!I(i)&&!ee(i),Qp=i=>an(i,Qh,Yh),I=i=>qh.test(i),ua=i=>an(i,Zh,Gp),os=i=>an(i,Wp,de),Km=i=>an(i,Vh,Yh),Zp=i=>an(i,Xh,Vp),ju=i=>an(i,Kh,Yp),ee=i=>Gh.test(i),ui=i=>nn(i,Zh),Kp=i=>nn(i,Fp),Jm=i=>nn(i,Vh),Jp=i=>nn(i,Qh),$p=i=>nn(i,Xh),_u=i=>nn(i,Kh,!0),an=(i,r,s)=>{const c=qh.exec(i);return c?c[1]?r(c[1]):s(c[2]):!1},nn=(i,r,s=!1)=>{const c=Gh.exec(i);return c?c[1]?r(c[1]):s:!1},Vh=i=>i==="position"||i==="percentage",Xh=i=>i==="image"||i==="url",Qh=i=>i==="length"||i==="size"||i==="bg-size",Zh=i=>i==="length",Wp=i=>i==="number",Fp=i=>i==="family-name",Kh=i=>i==="shadow",Pp=()=>{const i=Ze("color"),r=Ze("font"),s=Ze("text"),c=Ze("font-weight"),d=Ze("tracking"),h=Ze("leading"),v=Ze("breakpoint"),g=Ze("container"),x=Ze("spacing"),y=Ze("radius"),E=Ze("shadow"),T=Ze("inset-shadow"),R=Ze("text-shadow"),O=Ze("drop-shadow"),H=Ze("blur"),j=Ze("perspective"),z=Ze("aspect"),V=Ze("ease"),J=Ze("animate"),U=()=>["auto","avoid","all","avoid-page","page","left","right","column"],X=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],Z=()=>[...X(),ee,I],le=()=>["auto","hidden","clip","visible","scroll"],F=()=>["auto","contain","none"],L=()=>[ee,I,x],ne=()=>[Ka,"full","auto",...L()],re=()=>[Bl,"none","subgrid",ee,I],se=()=>["auto",{span:["full",Bl,ee,I]},Bl,ee,I],pe=()=>[Bl,"auto",ee,I],Ve=()=>["auto","min","max","fr",ee,I],ut=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Ne=()=>["start","end","center","stretch","center-safe","end-safe"],D=()=>["auto",...L()],K=()=>[Ka,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...L()],G=()=>[i,ee,I],be=()=>[...X(),Jm,Km,{position:[ee,I]}],S=()=>["no-repeat",{repeat:["","x","y","space","round"]}],Y=()=>["auto","cover","contain",Jp,Qp,{size:[ee,I]}],$=()=>[ss,ui,ua],Q=()=>["","none","full",y,ee,I],W=()=>["",de,ui,ua],he=()=>["solid","dashed","dotted","double"],ce=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],xe=()=>[de,ss,Jm,Km],Re=()=>["","none",H,ee,I],ht=()=>["none",de,ee,I],fl=()=>["none",de,ee,I],dl=()=>[de,ee,I],ml=()=>[Ka,"full",...L()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[sl],breakpoint:[sl],color:[qp],container:[sl],"drop-shadow":[sl],ease:["in","out","in-out"],font:[Xp],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[sl],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[sl],shadow:[sl],spacing:["px",de],text:[sl],"text-shadow":[sl],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",Ka,I,ee,z]}],container:["container"],columns:[{columns:[de,I,ee,g]}],"break-after":[{"break-after":U()}],"break-before":[{"break-before":U()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:Z()}],overflow:[{overflow:le()}],"overflow-x":[{"overflow-x":le()}],"overflow-y":[{"overflow-y":le()}],overscroll:[{overscroll:F()}],"overscroll-x":[{"overscroll-x":F()}],"overscroll-y":[{"overscroll-y":F()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:ne()}],"inset-x":[{"inset-x":ne()}],"inset-y":[{"inset-y":ne()}],start:[{start:ne()}],end:[{end:ne()}],top:[{top:ne()}],right:[{right:ne()}],bottom:[{bottom:ne()}],left:[{left:ne()}],visibility:["visible","invisible","collapse"],z:[{z:[Bl,"auto",ee,I]}],basis:[{basis:[Ka,"full","auto",g,...L()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[de,Ka,"auto","initial","none",I]}],grow:[{grow:["",de,ee,I]}],shrink:[{shrink:["",de,ee,I]}],order:[{order:[Bl,"first","last","none",ee,I]}],"grid-cols":[{"grid-cols":re()}],"col-start-end":[{col:se()}],"col-start":[{"col-start":pe()}],"col-end":[{"col-end":pe()}],"grid-rows":[{"grid-rows":re()}],"row-start-end":[{row:se()}],"row-start":[{"row-start":pe()}],"row-end":[{"row-end":pe()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Ve()}],"auto-rows":[{"auto-rows":Ve()}],gap:[{gap:L()}],"gap-x":[{"gap-x":L()}],"gap-y":[{"gap-y":L()}],"justify-content":[{justify:[...ut(),"normal"]}],"justify-items":[{"justify-items":[...Ne(),"normal"]}],"justify-self":[{"justify-self":["auto",...Ne()]}],"align-content":[{content:["normal",...ut()]}],"align-items":[{items:[...Ne(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Ne(),{baseline:["","last"]}]}],"place-content":[{"place-content":ut()}],"place-items":[{"place-items":[...Ne(),"baseline"]}],"place-self":[{"place-self":["auto",...Ne()]}],p:[{p:L()}],px:[{px:L()}],py:[{py:L()}],ps:[{ps:L()}],pe:[{pe:L()}],pt:[{pt:L()}],pr:[{pr:L()}],pb:[{pb:L()}],pl:[{pl:L()}],m:[{m:D()}],mx:[{mx:D()}],my:[{my:D()}],ms:[{ms:D()}],me:[{me:D()}],mt:[{mt:D()}],mr:[{mr:D()}],mb:[{mb:D()}],ml:[{ml:D()}],"space-x":[{"space-x":L()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":L()}],"space-y-reverse":["space-y-reverse"],size:[{size:K()}],w:[{w:[g,"screen",...K()]}],"min-w":[{"min-w":[g,"screen","none",...K()]}],"max-w":[{"max-w":[g,"screen","none","prose",{screen:[v]},...K()]}],h:[{h:["screen","lh",...K()]}],"min-h":[{"min-h":["screen","lh","none",...K()]}],"max-h":[{"max-h":["screen","lh",...K()]}],"font-size":[{text:["base",s,ui,ua]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[c,ee,os]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",ss,I]}],"font-family":[{font:[Kp,I,r]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[d,ee,I]}],"line-clamp":[{"line-clamp":[de,"none",ee,os]}],leading:[{leading:[h,...L()]}],"list-image":[{"list-image":["none",ee,I]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",ee,I]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:G()}],"text-color":[{text:G()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...he(),"wavy"]}],"text-decoration-thickness":[{decoration:[de,"from-font","auto",ee,ua]}],"text-decoration-color":[{decoration:G()}],"underline-offset":[{"underline-offset":[de,"auto",ee,I]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:L()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",ee,I]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",ee,I]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:be()}],"bg-repeat":[{bg:S()}],"bg-size":[{bg:Y()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},Bl,ee,I],radial:["",ee,I],conic:[Bl,ee,I]},$p,Zp]}],"bg-color":[{bg:G()}],"gradient-from-pos":[{from:$()}],"gradient-via-pos":[{via:$()}],"gradient-to-pos":[{to:$()}],"gradient-from":[{from:G()}],"gradient-via":[{via:G()}],"gradient-to":[{to:G()}],rounded:[{rounded:Q()}],"rounded-s":[{"rounded-s":Q()}],"rounded-e":[{"rounded-e":Q()}],"rounded-t":[{"rounded-t":Q()}],"rounded-r":[{"rounded-r":Q()}],"rounded-b":[{"rounded-b":Q()}],"rounded-l":[{"rounded-l":Q()}],"rounded-ss":[{"rounded-ss":Q()}],"rounded-se":[{"rounded-se":Q()}],"rounded-ee":[{"rounded-ee":Q()}],"rounded-es":[{"rounded-es":Q()}],"rounded-tl":[{"rounded-tl":Q()}],"rounded-tr":[{"rounded-tr":Q()}],"rounded-br":[{"rounded-br":Q()}],"rounded-bl":[{"rounded-bl":Q()}],"border-w":[{border:W()}],"border-w-x":[{"border-x":W()}],"border-w-y":[{"border-y":W()}],"border-w-s":[{"border-s":W()}],"border-w-e":[{"border-e":W()}],"border-w-t":[{"border-t":W()}],"border-w-r":[{"border-r":W()}],"border-w-b":[{"border-b":W()}],"border-w-l":[{"border-l":W()}],"divide-x":[{"divide-x":W()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":W()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...he(),"hidden","none"]}],"divide-style":[{divide:[...he(),"hidden","none"]}],"border-color":[{border:G()}],"border-color-x":[{"border-x":G()}],"border-color-y":[{"border-y":G()}],"border-color-s":[{"border-s":G()}],"border-color-e":[{"border-e":G()}],"border-color-t":[{"border-t":G()}],"border-color-r":[{"border-r":G()}],"border-color-b":[{"border-b":G()}],"border-color-l":[{"border-l":G()}],"divide-color":[{divide:G()}],"outline-style":[{outline:[...he(),"none","hidden"]}],"outline-offset":[{"outline-offset":[de,ee,I]}],"outline-w":[{outline:["",de,ui,ua]}],"outline-color":[{outline:G()}],shadow:[{shadow:["","none",E,_u,ju]}],"shadow-color":[{shadow:G()}],"inset-shadow":[{"inset-shadow":["none",T,_u,ju]}],"inset-shadow-color":[{"inset-shadow":G()}],"ring-w":[{ring:W()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:G()}],"ring-offset-w":[{"ring-offset":[de,ua]}],"ring-offset-color":[{"ring-offset":G()}],"inset-ring-w":[{"inset-ring":W()}],"inset-ring-color":[{"inset-ring":G()}],"text-shadow":[{"text-shadow":["none",R,_u,ju]}],"text-shadow-color":[{"text-shadow":G()}],opacity:[{opacity:[de,ee,I]}],"mix-blend":[{"mix-blend":[...ce(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ce()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[de]}],"mask-image-linear-from-pos":[{"mask-linear-from":xe()}],"mask-image-linear-to-pos":[{"mask-linear-to":xe()}],"mask-image-linear-from-color":[{"mask-linear-from":G()}],"mask-image-linear-to-color":[{"mask-linear-to":G()}],"mask-image-t-from-pos":[{"mask-t-from":xe()}],"mask-image-t-to-pos":[{"mask-t-to":xe()}],"mask-image-t-from-color":[{"mask-t-from":G()}],"mask-image-t-to-color":[{"mask-t-to":G()}],"mask-image-r-from-pos":[{"mask-r-from":xe()}],"mask-image-r-to-pos":[{"mask-r-to":xe()}],"mask-image-r-from-color":[{"mask-r-from":G()}],"mask-image-r-to-color":[{"mask-r-to":G()}],"mask-image-b-from-pos":[{"mask-b-from":xe()}],"mask-image-b-to-pos":[{"mask-b-to":xe()}],"mask-image-b-from-color":[{"mask-b-from":G()}],"mask-image-b-to-color":[{"mask-b-to":G()}],"mask-image-l-from-pos":[{"mask-l-from":xe()}],"mask-image-l-to-pos":[{"mask-l-to":xe()}],"mask-image-l-from-color":[{"mask-l-from":G()}],"mask-image-l-to-color":[{"mask-l-to":G()}],"mask-image-x-from-pos":[{"mask-x-from":xe()}],"mask-image-x-to-pos":[{"mask-x-to":xe()}],"mask-image-x-from-color":[{"mask-x-from":G()}],"mask-image-x-to-color":[{"mask-x-to":G()}],"mask-image-y-from-pos":[{"mask-y-from":xe()}],"mask-image-y-to-pos":[{"mask-y-to":xe()}],"mask-image-y-from-color":[{"mask-y-from":G()}],"mask-image-y-to-color":[{"mask-y-to":G()}],"mask-image-radial":[{"mask-radial":[ee,I]}],"mask-image-radial-from-pos":[{"mask-radial-from":xe()}],"mask-image-radial-to-pos":[{"mask-radial-to":xe()}],"mask-image-radial-from-color":[{"mask-radial-from":G()}],"mask-image-radial-to-color":[{"mask-radial-to":G()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":X()}],"mask-image-conic-pos":[{"mask-conic":[de]}],"mask-image-conic-from-pos":[{"mask-conic-from":xe()}],"mask-image-conic-to-pos":[{"mask-conic-to":xe()}],"mask-image-conic-from-color":[{"mask-conic-from":G()}],"mask-image-conic-to-color":[{"mask-conic-to":G()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:be()}],"mask-repeat":[{mask:S()}],"mask-size":[{mask:Y()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",ee,I]}],filter:[{filter:["","none",ee,I]}],blur:[{blur:Re()}],brightness:[{brightness:[de,ee,I]}],contrast:[{contrast:[de,ee,I]}],"drop-shadow":[{"drop-shadow":["","none",O,_u,ju]}],"drop-shadow-color":[{"drop-shadow":G()}],grayscale:[{grayscale:["",de,ee,I]}],"hue-rotate":[{"hue-rotate":[de,ee,I]}],invert:[{invert:["",de,ee,I]}],saturate:[{saturate:[de,ee,I]}],sepia:[{sepia:["",de,ee,I]}],"backdrop-filter":[{"backdrop-filter":["","none",ee,I]}],"backdrop-blur":[{"backdrop-blur":Re()}],"backdrop-brightness":[{"backdrop-brightness":[de,ee,I]}],"backdrop-contrast":[{"backdrop-contrast":[de,ee,I]}],"backdrop-grayscale":[{"backdrop-grayscale":["",de,ee,I]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[de,ee,I]}],"backdrop-invert":[{"backdrop-invert":["",de,ee,I]}],"backdrop-opacity":[{"backdrop-opacity":[de,ee,I]}],"backdrop-saturate":[{"backdrop-saturate":[de,ee,I]}],"backdrop-sepia":[{"backdrop-sepia":["",de,ee,I]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":L()}],"border-spacing-x":[{"border-spacing-x":L()}],"border-spacing-y":[{"border-spacing-y":L()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",ee,I]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[de,"initial",ee,I]}],ease:[{ease:["linear","initial",V,ee,I]}],delay:[{delay:[de,ee,I]}],animate:[{animate:["none",J,ee,I]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[j,ee,I]}],"perspective-origin":[{"perspective-origin":Z()}],rotate:[{rotate:ht()}],"rotate-x":[{"rotate-x":ht()}],"rotate-y":[{"rotate-y":ht()}],"rotate-z":[{"rotate-z":ht()}],scale:[{scale:fl()}],"scale-x":[{"scale-x":fl()}],"scale-y":[{"scale-y":fl()}],"scale-z":[{"scale-z":fl()}],"scale-3d":["scale-3d"],skew:[{skew:dl()}],"skew-x":[{"skew-x":dl()}],"skew-y":[{"skew-y":dl()}],transform:[{transform:[ee,I,"","none","gpu","cpu"]}],"transform-origin":[{origin:Z()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:ml()}],"translate-x":[{"translate-x":ml()}],"translate-y":[{"translate-y":ml()}],"translate-z":[{"translate-z":ml()}],"translate-none":["translate-none"],accent:[{accent:G()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:G()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",ee,I]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":L()}],"scroll-mx":[{"scroll-mx":L()}],"scroll-my":[{"scroll-my":L()}],"scroll-ms":[{"scroll-ms":L()}],"scroll-me":[{"scroll-me":L()}],"scroll-mt":[{"scroll-mt":L()}],"scroll-mr":[{"scroll-mr":L()}],"scroll-mb":[{"scroll-mb":L()}],"scroll-ml":[{"scroll-ml":L()}],"scroll-p":[{"scroll-p":L()}],"scroll-px":[{"scroll-px":L()}],"scroll-py":[{"scroll-py":L()}],"scroll-ps":[{"scroll-ps":L()}],"scroll-pe":[{"scroll-pe":L()}],"scroll-pt":[{"scroll-pt":L()}],"scroll-pr":[{"scroll-pr":L()}],"scroll-pb":[{"scroll-pb":L()}],"scroll-pl":[{"scroll-pl":L()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",ee,I]}],fill:[{fill:["none",...G()]}],"stroke-w":[{stroke:[de,ui,ua,os]}],stroke:[{stroke:["none",...G()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}},Ip=Op(Pp);function He(...i){return Ip(kh(i))}function eb({className:i,...r}){return f.jsx(gp,{"data-slot":"tabs",className:He("flex flex-col gap-2",i),...r})}function tb({className:i,...r}){return f.jsx(yp,{"data-slot":"tabs-list",className:He("bg-muted text-muted-foreground inline-flex h-9 w-fit items-center justify-center rounded-lg p-[3px]",i),...r})}function $m({className:i,...r}){return f.jsx(pp,{"data-slot":"tabs-trigger",className:He("data-[state=active]:bg-background dark:data-[state=active]:text-foreground focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:outline-ring dark:data-[state=active]:border-input dark:data-[state=active]:bg-input/30 text-foreground dark:text-muted-foreground inline-flex h-[calc(100%-1px)] flex-1 items-center justify-center gap-1.5 rounded-md border border-transparent px-2 py-1 text-sm font-medium whitespace-nowrap transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:shadow-sm [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",i),...r})}function Wm({className:i,...r}){return f.jsx(bp,{"data-slot":"tabs-content",className:He("flex-1 outline-none",i),...r})}const Fm=i=>typeof i=="boolean"?`${i}`:i===0?"0":i,Pm=kh,Jh=(i,r)=>s=>{var c;if((r==null?void 0:r.variants)==null)return Pm(i,s==null?void 0:s.class,s==null?void 0:s.className);const{variants:d,defaultVariants:h}=r,v=Object.keys(d).map(y=>{const E=s==null?void 0:s[y],T=h==null?void 0:h[y];if(E===null)return null;const R=Fm(E)||Fm(T);return d[y][R]}),g=s&&Object.entries(s).reduce((y,E)=>{let[T,R]=E;return R===void 0||(y[T]=R),y},{}),x=r==null||(c=r.compoundVariants)===null||c===void 0?void 0:c.reduce((y,E)=>{let{class:T,className:R,...O}=E;return Object.entries(O).every(H=>{let[j,z]=H;return Array.isArray(z)?z.includes({...h,...g}[j]):{...h,...g}[j]===z})?[...y,T,R]:y},[]);return Pm(i,v,x,s==null?void 0:s.class,s==null?void 0:s.className)},lb=Jh("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function lt({className:i,variant:r,size:s,asChild:c=!1,...d}){const h=c?yh:"button";return f.jsx(h,{"data-slot":"button",className:He(lb({variant:r,size:s,className:i})),...d})}function rt({className:i,...r}){return f.jsx("div",{"data-slot":"card",className:He("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",i),...r})}function kt({className:i,...r}){return f.jsx("div",{"data-slot":"card-header",className:He("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",i),...r})}function Bt({className:i,...r}){return f.jsx("div",{"data-slot":"card-title",className:He("leading-none font-semibold",i),...r})}function en({className:i,...r}){return f.jsx("div",{"data-slot":"card-description",className:He("text-muted-foreground text-sm",i),...r})}function Tt({className:i,...r}){return f.jsx("div",{"data-slot":"card-content",className:He("px-6",i),...r})}const ab=Jh("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function Kt({className:i,variant:r,asChild:s=!1,...c}){const d=s?yh:"span";return f.jsx(d,{"data-slot":"badge",className:He(ab({variant:r}),i),...c})}/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const nb=i=>i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),ib=i=>i.replace(/^([A-Z])|[\s-_]+(\w)/g,(r,s,c)=>c?c.toUpperCase():s.toLowerCase()),Im=i=>{const r=ib(i);return r.charAt(0).toUpperCase()+r.slice(1)},$h=(...i)=>i.filter((r,s,c)=>!!r&&r.trim()!==""&&c.indexOf(r)===s).join(" ").trim(),ub=i=>{for(const r in i)if(r.startsWith("aria-")||r==="role"||r==="title")return!0};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */var cb={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const rb=b.forwardRef(({color:i="currentColor",size:r=24,strokeWidth:s=2,absoluteStrokeWidth:c,className:d="",children:h,iconNode:v,...g},x)=>b.createElement("svg",{ref:x,...cb,width:r,height:r,stroke:i,strokeWidth:c?Number(s)*24/Number(r):s,className:$h("lucide",d),...!h&&!ub(g)&&{"aria-hidden":"true"},...g},[...v.map(([y,E])=>b.createElement(y,E)),...Array.isArray(h)?h:[h]]));/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Je=(i,r)=>{const s=b.forwardRef(({className:c,...d},h)=>b.createElement(rb,{ref:h,iconNode:r,className:$h(`lucide-${nb(Im(i))}`,`lucide-${i}`,c),...d}));return s.displayName=Im(i),s};/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const sb=[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]],Wh=Je("brain",sb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const ob=[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]],Fh=Je("calendar",ob);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const fb=[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]],db=Je("check",fb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const mb=[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]],hb=Je("circle-check-big",mb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const vb=[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]],gb=Je("clock",vb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const yb=[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]],ol=Je("file-text",yb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const pb=[["path",{d:"m6 14 1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2",key:"usdka0"}]],bb=Je("folder-open",pb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const xb=[["line",{x1:"4",x2:"20",y1:"9",y2:"9",key:"4lhtct"}],["line",{x1:"4",x2:"20",y1:"15",y2:"15",key:"vyu0kd"}],["line",{x1:"10",x2:"8",y1:"3",y2:"21",key:"1ggp8o"}],["line",{x1:"16",x2:"14",y1:"3",y2:"21",key:"weycgp"}]],Ns=Je("hash",xb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Sb=[["rect",{x:"16",y:"16",width:"6",height:"6",rx:"1",key:"4q2zg0"}],["rect",{x:"2",y:"16",width:"6",height:"6",rx:"1",key:"8cvhb9"}],["rect",{x:"9",y:"2",width:"6",height:"6",rx:"1",key:"1egb70"}],["path",{d:"M5 16v-3a1 1 0 0 1 1-1h12a1 1 0 0 1 1 1v3",key:"1jsf9p"}],["path",{d:"M12 12V8",key:"2874zd"}]],Ts=Je("network",Sb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Eb=[["path",{d:"M12 20h9",key:"t2du7b"}],["path",{d:"M16.376 3.622a1 1 0 0 1 3.002 3.002L7.368 18.635a2 2 0 0 1-.855.506l-2.872.838a.5.5 0 0 1-.62-.62l.838-2.872a2 2 0 0 1 .506-.854z",key:"1ykcvy"}]],Nb=Je("pen-line",Eb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Tb=[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]],eh=Je("plus",Tb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ab=[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]],jb=Je("rotate-ccw",Ab);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const _b=[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]],Mb=Je("save",_b);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Rb=[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]],Ph=Je("search",Rb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const wb=[["path",{d:"M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z",key:"1qme2f"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]],Ih=Je("settings",wb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Cb=[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]],ev=Je("sparkles",Cb);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Db=[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]],Bu=Je("tag",Db);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const Ob=[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]],fs=Je("upload",Ob);/**
 * @license lucide-react v0.510.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const zb=[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]],Uu=Je("x",zb);function As({className:i,type:r,...s}){return f.jsx("input",{type:r,"data-slot":"input",className:He("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",i),...s})}const Ub=({onNoteSelect:i,selectedNoteId:r})=>{const[s,c]=b.useState([]),[d,h]=b.useState(!0),[v,g]=b.useState(""),[x,y]=b.useState([]);b.useEffect(()=>{E()},[]),b.useEffect(()=>{if(v.trim()==="")y(s);else{const O=s.filter(H=>{var j;return H.title.toLowerCase().includes(v.toLowerCase())||H.content.toLowerCase().includes(v.toLowerCase())||((j=H.summary)==null?void 0:j.toLowerCase().includes(v.toLowerCase()))||H.tags.some(z=>z.name.toLowerCase().includes(v.toLowerCase()))});y(O)}},[v,s]);const E=async()=>{try{const H=await(await fetch("/api/notes")).json();c(H.notes||[]),h(!1)}catch(O){console.error("Error fetching notes:",O),h(!1)}},T=O=>new Date(O).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"}),R=(O,H=150)=>O?O.length>H?O.substring(0,H)+"...":O:"";return d?f.jsx("div",{className:"flex items-center justify-center h-64",children:f.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary"})}):f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{className:"relative",children:[f.jsx(Ph,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4"}),f.jsx(As,{placeholder:"Search notes, content, tags...",value:v,onChange:O=>g(O.target.value),className:"pl-10"})]}),f.jsxs("div",{className:"text-sm text-muted-foreground",children:[x.length," ",x.length===1?"note":"notes",v&&` matching "${v}"`]}),f.jsx("div",{className:"space-y-3",children:x.map(O=>f.jsxs(rt,{className:`cursor-pointer transition-all duration-200 hover:shadow-md ${r===O.id?"ring-2 ring-primary":""}`,onClick:()=>i(O),children:[f.jsx(kt,{className:"pb-3",children:f.jsx("div",{className:"flex items-start justify-between",children:f.jsxs("div",{className:"flex-1",children:[f.jsx(Bt,{className:"text-lg font-semibold line-clamp-1",children:O.title}),f.jsxs(en,{className:"flex items-center gap-2 mt-1",children:[f.jsx(ol,{className:"h-3 w-3"}),f.jsx("span",{className:"uppercase text-xs",children:O.file_type}),f.jsx(Fh,{className:"h-3 w-3 ml-2"}),f.jsx("span",{className:"text-xs",children:T(O.updated_at)})]})]})})}),f.jsxs(Tt,{className:"pt-0",children:[O.summary&&f.jsx("p",{className:"text-sm text-muted-foreground mb-3 line-clamp-2",children:R(O.summary)}),O.tags&&O.tags.length>0&&f.jsxs("div",{className:"flex flex-wrap gap-1 mb-3",children:[O.tags.slice(0,3).map(H=>f.jsxs(Kt,{variant:"secondary",className:"text-xs",children:[f.jsx(Bu,{className:"h-2 w-2 mr-1"}),H.name]},H.id)),O.tags.length>3&&f.jsxs(Kt,{variant:"outline",className:"text-xs",children:["+",O.tags.length-3," more"]})]}),O.topics&&O.topics.length>0&&f.jsxs("div",{className:"flex flex-wrap gap-1",children:[O.topics.slice(0,2).map((H,j)=>f.jsx(Kt,{variant:"outline",className:"text-xs bg-blue-50 text-blue-700 border-blue-200",children:H},j)),O.topics.length>2&&f.jsxs(Kt,{variant:"outline",className:"text-xs",children:["+",O.topics.length-2," topics"]})]})]})]},O.id))}),x.length===0&&!d&&f.jsxs("div",{className:"text-center py-12",children:[f.jsx(ol,{className:"h-12 w-12 text-muted-foreground mx-auto mb-4"}),f.jsx("h3",{className:"text-lg font-semibold mb-2",children:"No notes found"}),f.jsx("p",{className:"text-muted-foreground",children:v?"Try adjusting your search terms":"Import some notes to get started"})]})]})};function Hb({className:i,...r}){return f.jsx("textarea",{"data-slot":"textarea",className:He("border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",i),...r})}const kb=({note:i,onNoteUpdate:r})=>{const[s,c]=b.useState(!1),[d,h]=b.useState(""),[v,g]=b.useState(!1),[x,y]=b.useState(!1);if(b.useEffect(()=>{i&&h(i.content)},[i]),!i)return f.jsx("div",{className:"flex items-center justify-center h-full",children:f.jsxs("div",{className:"text-center",children:[f.jsx(ol,{className:"h-16 w-16 text-muted-foreground mx-auto mb-4"}),f.jsx("h3",{className:"text-xl font-semibold mb-2",children:"Select a note"}),f.jsx("p",{className:"text-muted-foreground",children:"Choose a note from the list to view its content"})]})});const E=async()=>{g(!0);try{const H=await fetch(`/api/notes/${i.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({content:d,process_with_llm:!0})});if(H.ok){const j=await H.json();r(j),c(!1)}else console.error("Failed to save note")}catch(H){console.error("Error saving note:",H)}finally{g(!1)}},T=async()=>{y(!0);try{const H=await fetch(`/api/llm/process/${i.id}`,{method:"POST"});if(H.ok){const j=await H.json();r(j.note)}else console.error("Failed to process note with LLM")}catch(H){console.error("Error processing note:",H)}finally{y(!1)}},R=H=>new Date(H).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}),O=H=>H.split(`
`).map((j,z)=>j.startsWith("# ")?f.jsx("h1",{className:"text-2xl font-bold mb-4 mt-6",children:j.substring(2)},z):j.startsWith("## ")?f.jsx("h2",{className:"text-xl font-semibold mb-3 mt-5",children:j.substring(3)},z):j.startsWith("### ")?f.jsx("h3",{className:"text-lg font-medium mb-2 mt-4",children:j.substring(4)},z):j.startsWith("- ")||j.startsWith("* ")?f.jsx("li",{className:"ml-4 mb-1",children:j.substring(2)},z):j.trim()===""?f.jsx("br",{},z):f.jsx("p",{className:"mb-2 leading-relaxed",children:j},z));return f.jsxs("div",{className:"h-full flex flex-col",children:[f.jsx(rt,{className:"mb-4",children:f.jsx(kt,{children:f.jsxs("div",{className:"flex items-start justify-between",children:[f.jsxs("div",{className:"flex-1",children:[f.jsx(Bt,{className:"text-2xl font-bold mb-2",children:i.title}),f.jsxs(en,{className:"flex items-center gap-4 text-sm",children:[f.jsxs("div",{className:"flex items-center gap-1",children:[f.jsx(ol,{className:"h-4 w-4"}),f.jsx("span",{className:"uppercase font-medium",children:i.file_type})]}),f.jsxs("div",{className:"flex items-center gap-1",children:[f.jsx(Fh,{className:"h-4 w-4"}),f.jsxs("span",{children:["Updated ",R(i.updated_at)]})]}),f.jsxs("div",{className:"flex items-center gap-1",children:[f.jsx(gb,{className:"h-4 w-4"}),f.jsxs("span",{children:["Created ",R(i.created_at)]})]})]})]}),f.jsxs("div",{className:"flex gap-2",children:[f.jsxs(lt,{variant:"outline",size:"sm",onClick:T,disabled:x,children:[x?f.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-primary"}):f.jsx(ev,{className:"h-4 w-4"}),x?"Processing...":"Process with LLM"]}),s?f.jsxs("div",{className:"flex gap-2",children:[f.jsxs(lt,{variant:"outline",size:"sm",onClick:()=>{c(!1),h(i.content)},children:[f.jsx(Uu,{className:"h-4 w-4 mr-2"}),"Cancel"]}),f.jsxs(lt,{size:"sm",onClick:E,disabled:v,children:[v?f.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white"}):f.jsx(Mb,{className:"h-4 w-4 mr-2"}),v?"Saving...":"Save"]})]}):f.jsxs(lt,{variant:"outline",size:"sm",onClick:()=>c(!0),children:[f.jsx(Nb,{className:"h-4 w-4 mr-2"}),"Edit"]})]})]})})}),f.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4 mb-4",children:[i.summary&&f.jsxs(rt,{children:[f.jsx(kt,{className:"pb-3",children:f.jsxs(Bt,{className:"text-sm font-medium flex items-center gap-2",children:[f.jsx(Wh,{className:"h-4 w-4"}),"Summary"]})}),f.jsx(Tt,{className:"pt-0",children:f.jsx("p",{className:"text-sm text-muted-foreground leading-relaxed",children:i.summary})})]}),i.tags&&i.tags.length>0&&f.jsxs(rt,{children:[f.jsx(kt,{className:"pb-3",children:f.jsxs(Bt,{className:"text-sm font-medium flex items-center gap-2",children:[f.jsx(Bu,{className:"h-4 w-4"}),"Tags (",i.tags.length,")"]})}),f.jsx(Tt,{className:"pt-0",children:f.jsx("div",{className:"flex flex-wrap gap-2",children:i.tags.map(H=>f.jsx(Kt,{variant:"secondary",className:"text-xs",children:H.name},H.id))})})]}),i.topics&&i.topics.length>0&&f.jsxs(rt,{children:[f.jsx(kt,{className:"pb-3",children:f.jsxs(Bt,{className:"text-sm font-medium flex items-center gap-2",children:[f.jsx(Ns,{className:"h-4 w-4"}),"Topics (",i.topics.length,")"]})}),f.jsx(Tt,{className:"pt-0",children:f.jsx("div",{className:"flex flex-wrap gap-2",children:i.topics.map((H,j)=>f.jsx(Kt,{variant:"outline",className:"text-xs bg-blue-50 text-blue-700 border-blue-200",children:H},j))})})]})]}),f.jsxs(rt,{className:"flex-1 flex flex-col",children:[f.jsx(kt,{children:f.jsx(Bt,{className:"text-lg",children:"Content"})}),f.jsx(Tt,{className:"flex-1 flex flex-col",children:s?f.jsx(Hb,{value:d,onChange:H=>h(H.target.value),className:"flex-1 min-h-[400px] font-mono text-sm resize-none",placeholder:"Enter note content..."}):f.jsx("div",{className:"flex-1 overflow-auto",children:f.jsx("div",{className:"prose prose-sm max-w-none",children:O(i.content)})})})]})]})};function tv(i,[r,s]){return Math.min(s,Math.max(r,i))}function lv(i){const r=b.useRef({value:i,previous:i});return b.useMemo(()=>(r.current.value!==i&&(r.current.previous=r.current.value,r.current.value=i),r.current.previous),[i])}function av(i){const[r,s]=b.useState(void 0);return Pa(()=>{if(i){s({width:i.offsetWidth,height:i.offsetHeight});const c=new ResizeObserver(d=>{if(!Array.isArray(d)||!d.length)return;const h=d[0];let v,g;if("borderBoxSize"in h){const x=h.borderBoxSize,y=Array.isArray(x)?x[0]:x;v=y.inlineSize,g=y.blockSize}else v=i.offsetWidth,g=i.offsetHeight;s({width:v,height:g})});return c.observe(i,{box:"border-box"}),()=>c.unobserve(i)}else s(void 0)},[i]),r}var nv=["PageUp","PageDown"],iv=["ArrowUp","ArrowDown","ArrowLeft","ArrowRight"],uv={"from-left":["Home","PageDown","ArrowDown","ArrowLeft"],"from-right":["Home","PageDown","ArrowDown","ArrowRight"],"from-bottom":["Home","PageDown","ArrowDown","ArrowLeft"],"from-top":["Home","PageDown","ArrowUp","ArrowLeft"]},un="Slider",[js,Bb,Lb]=ph(un),[cv,Yx]=tn(un,[Lb]),[qb,Lu]=cv(un),rv=b.forwardRef((i,r)=>{const{name:s,min:c=0,max:d=100,step:h=1,orientation:v="horizontal",disabled:g=!1,minStepsBetweenThumbs:x=0,defaultValue:y=[c],value:E,onValueChange:T=()=>{},onValueCommit:R=()=>{},inverted:O=!1,form:H,...j}=i,z=b.useRef(new Set),V=b.useRef(0),U=v==="horizontal"?Gb:Yb,[X=[],Z]=si({prop:E,defaultProp:y,onChange:se=>{var Ve;(Ve=[...z.current][V.current])==null||Ve.focus(),T(se)}}),le=b.useRef(X);function F(se){const pe=Kb(X,se);re(se,pe)}function L(se){re(se,V.current)}function ne(){const se=le.current[V.current];X[V.current]!==se&&R(X)}function re(se,pe,{commit:Ve}={commit:!1}){const ut=Fb(h),Ne=Pb(Math.round((se-c)/h)*h+c,ut),D=tv(Ne,[c,d]);Z((K=[])=>{const G=Qb(K,D,pe);if(Wb(G,x*h)){V.current=G.indexOf(D);const be=String(G)!==String(K);return be&&Ve&&R(G),be?G:K}else return K})}return f.jsx(qb,{scope:i.__scopeSlider,name:s,disabled:g,min:c,max:d,valueIndexToChangeRef:V,thumbs:z.current,values:X,orientation:v,form:H,children:f.jsx(js.Provider,{scope:i.__scopeSlider,children:f.jsx(js.Slot,{scope:i.__scopeSlider,children:f.jsx(U,{"aria-disabled":g,"data-disabled":g?"":void 0,...j,ref:r,onPointerDown:De(j.onPointerDown,()=>{g||(le.current=X)}),min:c,max:d,inverted:O,onSlideStart:g?void 0:F,onSlideMove:g?void 0:L,onSlideEnd:g?void 0:ne,onHomeKeyDown:()=>!g&&re(c,0,{commit:!0}),onEndKeyDown:()=>!g&&re(d,X.length-1,{commit:!0}),onStepKeyDown:({event:se,direction:pe})=>{if(!g){const Ne=nv.includes(se.key)||se.shiftKey&&iv.includes(se.key)?10:1,D=V.current,K=X[D],G=h*Ne*pe;re(K+G,D,{commit:!0})}}})})})})});rv.displayName=un;var[sv,ov]=cv(un,{startEdge:"left",endEdge:"right",size:"width",direction:1}),Gb=b.forwardRef((i,r)=>{const{min:s,max:c,dir:d,inverted:h,onSlideStart:v,onSlideMove:g,onSlideEnd:x,onStepKeyDown:y,...E}=i,[T,R]=b.useState(null),O=Ke(r,U=>R(U)),H=b.useRef(void 0),j=Ds(d),z=j==="ltr",V=z&&!h||!z&&h;function J(U){const X=H.current||T.getBoundingClientRect(),Z=[0,X.width],F=Us(Z,V?[s,c]:[c,s]);return H.current=X,F(U-X.left)}return f.jsx(sv,{scope:i.__scopeSlider,startEdge:V?"left":"right",endEdge:V?"right":"left",direction:V?1:-1,size:"width",children:f.jsx(fv,{dir:j,"data-orientation":"horizontal",...E,ref:O,style:{...E.style,"--radix-slider-thumb-transform":"translateX(-50%)"},onSlideStart:U=>{const X=J(U.clientX);v==null||v(X)},onSlideMove:U=>{const X=J(U.clientX);g==null||g(X)},onSlideEnd:()=>{H.current=void 0,x==null||x()},onStepKeyDown:U=>{const Z=uv[V?"from-left":"from-right"].includes(U.key);y==null||y({event:U,direction:Z?-1:1})}})})}),Yb=b.forwardRef((i,r)=>{const{min:s,max:c,inverted:d,onSlideStart:h,onSlideMove:v,onSlideEnd:g,onStepKeyDown:x,...y}=i,E=b.useRef(null),T=Ke(r,E),R=b.useRef(void 0),O=!d;function H(j){const z=R.current||E.current.getBoundingClientRect(),V=[0,z.height],U=Us(V,O?[c,s]:[s,c]);return R.current=z,U(j-z.top)}return f.jsx(sv,{scope:i.__scopeSlider,startEdge:O?"bottom":"top",endEdge:O?"top":"bottom",size:"height",direction:O?1:-1,children:f.jsx(fv,{"data-orientation":"vertical",...y,ref:T,style:{...y.style,"--radix-slider-thumb-transform":"translateY(50%)"},onSlideStart:j=>{const z=H(j.clientY);h==null||h(z)},onSlideMove:j=>{const z=H(j.clientY);v==null||v(z)},onSlideEnd:()=>{R.current=void 0,g==null||g()},onStepKeyDown:j=>{const V=uv[O?"from-bottom":"from-top"].includes(j.key);x==null||x({event:j,direction:V?-1:1})}})})}),fv=b.forwardRef((i,r)=>{const{__scopeSlider:s,onSlideStart:c,onSlideMove:d,onSlideEnd:h,onHomeKeyDown:v,onEndKeyDown:g,onStepKeyDown:x,...y}=i,E=Lu(un,s);return f.jsx(ze.span,{...y,ref:r,onKeyDown:De(i.onKeyDown,T=>{T.key==="Home"?(v(T),T.preventDefault()):T.key==="End"?(g(T),T.preventDefault()):nv.concat(iv).includes(T.key)&&(x(T),T.preventDefault())}),onPointerDown:De(i.onPointerDown,T=>{const R=T.target;R.setPointerCapture(T.pointerId),T.preventDefault(),E.thumbs.has(R)?R.focus():c(T)}),onPointerMove:De(i.onPointerMove,T=>{T.target.hasPointerCapture(T.pointerId)&&d(T)}),onPointerUp:De(i.onPointerUp,T=>{const R=T.target;R.hasPointerCapture(T.pointerId)&&(R.releasePointerCapture(T.pointerId),h(T))})})}),dv="SliderTrack",mv=b.forwardRef((i,r)=>{const{__scopeSlider:s,...c}=i,d=Lu(dv,s);return f.jsx(ze.span,{"data-disabled":d.disabled?"":void 0,"data-orientation":d.orientation,...c,ref:r})});mv.displayName=dv;var _s="SliderRange",hv=b.forwardRef((i,r)=>{const{__scopeSlider:s,...c}=i,d=Lu(_s,s),h=ov(_s,s),v=b.useRef(null),g=Ke(r,v),x=d.values.length,y=d.values.map(R=>yv(R,d.min,d.max)),E=x>1?Math.min(...y):0,T=100-Math.max(...y);return f.jsx(ze.span,{"data-orientation":d.orientation,"data-disabled":d.disabled?"":void 0,...c,ref:g,style:{...i.style,[h.startEdge]:E+"%",[h.endEdge]:T+"%"}})});hv.displayName=_s;var Ms="SliderThumb",vv=b.forwardRef((i,r)=>{const s=Bb(i.__scopeSlider),[c,d]=b.useState(null),h=Ke(r,g=>d(g)),v=b.useMemo(()=>c?s().findIndex(g=>g.ref.current===c):-1,[s,c]);return f.jsx(Vb,{...i,ref:h,index:v})}),Vb=b.forwardRef((i,r)=>{const{__scopeSlider:s,index:c,name:d,...h}=i,v=Lu(Ms,s),g=ov(Ms,s),[x,y]=b.useState(null),E=Ke(r,J=>y(J)),T=x?v.form||!!x.closest("form"):!0,R=av(x),O=v.values[c],H=O===void 0?0:yv(O,v.min,v.max),j=Zb(c,v.values.length),z=R==null?void 0:R[g.size],V=z?Jb(z,H,g.direction):0;return b.useEffect(()=>{if(x)return v.thumbs.add(x),()=>{v.thumbs.delete(x)}},[x,v.thumbs]),f.jsxs("span",{style:{transform:"var(--radix-slider-thumb-transform)",position:"absolute",[g.startEdge]:`calc(${H}% + ${V}px)`},children:[f.jsx(js.ItemSlot,{scope:i.__scopeSlider,children:f.jsx(ze.span,{role:"slider","aria-label":i["aria-label"]||j,"aria-valuemin":v.min,"aria-valuenow":O,"aria-valuemax":v.max,"aria-orientation":v.orientation,"data-orientation":v.orientation,"data-disabled":v.disabled?"":void 0,tabIndex:v.disabled?void 0:0,...h,ref:E,style:O===void 0?{display:"none"}:i.style,onFocus:De(i.onFocus,()=>{v.valueIndexToChangeRef.current=c})})}),T&&f.jsx(gv,{name:d??(v.name?v.name+(v.values.length>1?"[]":""):void 0),form:v.form,value:O},c)]})});vv.displayName=Ms;var Xb="RadioBubbleInput",gv=b.forwardRef(({__scopeSlider:i,value:r,...s},c)=>{const d=b.useRef(null),h=Ke(d,c),v=lv(r);return b.useEffect(()=>{const g=d.current;if(!g)return;const x=window.HTMLInputElement.prototype,E=Object.getOwnPropertyDescriptor(x,"value").set;if(v!==r&&E){const T=new Event("input",{bubbles:!0});E.call(g,r),g.dispatchEvent(T)}},[v,r]),f.jsx(ze.input,{style:{display:"none"},...s,ref:h,defaultValue:r})});gv.displayName=Xb;function Qb(i=[],r,s){const c=[...i];return c[s]=r,c.sort((d,h)=>d-h)}function yv(i,r,s){const h=100/(s-r)*(i-r);return tv(h,[0,100])}function Zb(i,r){return r>2?`Value ${i+1} of ${r}`:r===2?["Minimum","Maximum"][i]:void 0}function Kb(i,r){if(i.length===1)return 0;const s=i.map(d=>Math.abs(d-r)),c=Math.min(...s);return s.indexOf(c)}function Jb(i,r,s){const c=i/2,h=Us([0,50],[0,c]);return(c-h(r)*s)*s}function $b(i){return i.slice(0,-1).map((r,s)=>i[s+1]-r)}function Wb(i,r){if(r>0){const s=$b(i);return Math.min(...s)>=r}return!0}function Us(i,r){return s=>{if(i[0]===i[1]||r[0]===r[1])return r[0];const c=(r[1]-r[0])/(i[1]-i[0]);return r[0]+c*(s-i[0])}}function Fb(i){return(String(i).split(".")[1]||"").length}function Pb(i,r){const s=Math.pow(10,r);return Math.round(i*s)/s}var Ib=rv,e1=mv,t1=hv,l1=vv;function a1({className:i,defaultValue:r,value:s,min:c=0,max:d=100,...h}){const v=b.useMemo(()=>Array.isArray(s)?s:Array.isArray(r)?r:[c,d],[s,r,c,d]);return f.jsxs(Ib,{"data-slot":"slider",defaultValue:r,value:s,min:c,max:d,className:He("relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",i),...h,children:[f.jsx(e1,{"data-slot":"slider-track",className:He("bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5"),children:f.jsx(t1,{"data-slot":"slider-range",className:He("bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full")})}),Array.from({length:v.length},(g,x)=>f.jsx(l1,{"data-slot":"slider-thumb",className:"border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"},x))]})}const n1=({onNodeSelect:i})=>{const[r,s]=b.useState({nodes:[],edges:[]}),[c,d]=b.useState(!0),[h,v]=b.useState(null),[g,x]=b.useState([50]),[y,E]=b.useState(!1),T=b.useRef(null),[R,O]=b.useState({width:800,height:600});b.useEffect(()=>{H();const U=()=>{if(T.current){const X=T.current.parentElement.getBoundingClientRect();O({width:X.width,height:X.height-100})}};return window.addEventListener("resize",U),U(),()=>window.removeEventListener("resize",U)},[]);const H=async()=>{try{const X=await(await fetch("/api/notes/graph")).json(),Z=j(X);s(Z),d(!1)}catch(U){console.error("Error fetching graph data:",U),d(!1)}},j=U=>{const{nodes:X,edges:Z}=U,le=X.map((F,L)=>{const ne=L/X.length*2*Math.PI,re=Math.min(R.width,R.height)*.3,se=R.width/2,pe=R.height/2;return{...F,x:se+Math.cos(ne)*re,y:pe+Math.sin(ne)*re,radius:Math.max(20,Math.min(40,F.tags.length*5+15))}});return{nodes:le,edges:Z.filter(F=>le.find(L=>L.id===F.source)&&le.find(L=>L.id===F.target))}},z=U=>{v(U),i&&i(U)},V=U=>U.type==="md"?"#3b82f6":U.type==="json"?"#10b981":"#6b7280",J=()=>{x([50])};return c?f.jsx("div",{className:"flex items-center justify-center h-full",children:f.jsxs("div",{className:"text-center",children:[f.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"}),f.jsx("p",{className:"text-muted-foreground",children:"Loading graph..."})]})}):r.nodes.length===0?f.jsx("div",{className:"flex items-center justify-center h-full",children:f.jsxs("div",{className:"text-center",children:[f.jsx(Ts,{className:"h-16 w-16 text-muted-foreground mx-auto mb-4"}),f.jsx("h3",{className:"text-xl font-semibold mb-2",children:"No graph data"}),f.jsx("p",{className:"text-muted-foreground",children:"Import some notes to see the knowledge graph"})]})}):f.jsxs("div",{className:"h-full flex flex-col",children:[f.jsx(rt,{className:"mb-4",children:f.jsx(kt,{className:"pb-3",children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{children:[f.jsxs(Bt,{className:"text-lg flex items-center gap-2",children:[f.jsx(Ts,{className:"h-5 w-5"}),"Knowledge Graph"]}),f.jsxs(en,{children:[r.nodes.length," notes, ",r.edges.length," connections"]})]}),f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsxs("div",{className:"flex items-center gap-2 mr-4",children:[f.jsx("span",{className:"text-sm text-muted-foreground",children:"Zoom:"}),f.jsx("div",{className:"w-24",children:f.jsx(a1,{value:g,onValueChange:x,max:100,min:10,step:5})})]}),f.jsx(lt,{variant:"outline",size:"sm",onClick:J,children:f.jsx(jb,{className:"h-4 w-4"})}),f.jsx(lt,{variant:"outline",size:"sm",onClick:()=>E(!y),children:f.jsx(Ih,{className:"h-4 w-4"})})]})]})})}),f.jsxs("div",{className:"flex-1 flex gap-4",children:[f.jsx(rt,{className:"flex-1",children:f.jsx(Tt,{className:"p-0 h-full",children:f.jsx("div",{className:"relative h-full overflow-hidden",children:f.jsxs("svg",{ref:T,width:R.width,height:R.height,className:"border rounded-lg",style:{transform:`scale(${g[0]/50})`},children:[f.jsx("g",{children:r.edges.map(U=>{const X=r.nodes.find(le=>le.id===U.source),Z=r.nodes.find(le=>le.id===U.target);return!X||!Z?null:f.jsx("line",{x1:X.x,y1:X.y,x2:Z.x,y2:Z.y,stroke:"#e5e7eb",strokeWidth:"2",opacity:"0.6"},U.id)})}),f.jsx("g",{children:r.nodes.map(U=>f.jsxs("g",{children:[f.jsx("circle",{cx:U.x,cy:U.y,r:U.radius,fill:V(U),stroke:(h==null?void 0:h.id)===U.id?"#f59e0b":"#ffffff",strokeWidth:(h==null?void 0:h.id)===U.id?3:2,className:"cursor-pointer hover:opacity-80 transition-opacity",onClick:()=>z(U)}),f.jsx("foreignObject",{x:U.x-8,y:U.y-8,width:"16",height:"16",className:"pointer-events-none",children:f.jsx(ol,{className:"h-4 w-4 text-white"})}),f.jsx("text",{x:U.x,y:U.y+U.radius+15,textAnchor:"middle",className:"text-xs fill-current text-gray-700 pointer-events-none",style:{fontSize:"12px"},children:U.title.length>15?U.title.substring(0,15)+"...":U.title})]},U.id))})]})})})}),h&&f.jsxs(rt,{className:"w-80",children:[f.jsxs(kt,{children:[f.jsxs(Bt,{className:"text-lg flex items-center gap-2",children:[f.jsx(ol,{className:"h-5 w-5"}),h.title]}),f.jsx(en,{className:"flex items-center gap-2",children:f.jsx("span",{className:"uppercase text-xs font-medium",children:h.type})})]}),f.jsxs(Tt,{className:"space-y-4",children:[h.summary&&f.jsxs("div",{children:[f.jsxs("h4",{className:"text-sm font-medium mb-2 flex items-center gap-2",children:[f.jsx(Ns,{className:"h-4 w-4"}),"Summary"]}),f.jsx("p",{className:"text-sm text-muted-foreground leading-relaxed",children:h.summary})]}),h.tags&&h.tags.length>0&&f.jsxs("div",{children:[f.jsxs("h4",{className:"text-sm font-medium mb-2 flex items-center gap-2",children:[f.jsx(Bu,{className:"h-4 w-4"}),"Tags"]}),f.jsx("div",{className:"flex flex-wrap gap-1",children:h.tags.map((U,X)=>f.jsx(Kt,{variant:"secondary",className:"text-xs",children:U},X))})]}),h.topics&&h.topics.length>0&&f.jsxs("div",{children:[f.jsxs("h4",{className:"text-sm font-medium mb-2 flex items-center gap-2",children:[f.jsx(Ns,{className:"h-4 w-4"}),"Topics"]}),f.jsx("div",{className:"flex flex-wrap gap-1",children:h.topics.map((U,X)=>f.jsx(Kt,{variant:"outline",className:"text-xs bg-blue-50 text-blue-700 border-blue-200",children:U},X))})]}),f.jsx("div",{className:"pt-2",children:f.jsx(lt,{size:"sm",className:"w-full",onClick:()=>i&&i(h),children:"View Note"})})]})]})]}),f.jsx(rt,{className:"mt-4",children:f.jsx(Tt,{className:"py-3",children:f.jsxs("div",{className:"flex items-center gap-6 text-sm",children:[f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("div",{className:"w-4 h-4 rounded-full bg-blue-500"}),f.jsx("span",{children:"Markdown Notes"})]}),f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("div",{className:"w-4 h-4 rounded-full bg-green-500"}),f.jsx("span",{children:"JSON Files"})]}),f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx("div",{className:"w-4 h-1 bg-gray-300"}),f.jsx("span",{children:"Connections"})]})]})})})]})};function i1(i,r=globalThis==null?void 0:globalThis.document){const s=Ia(i);b.useEffect(()=>{const c=d=>{d.key==="Escape"&&s(d)};return r.addEventListener("keydown",c,{capture:!0}),()=>r.removeEventListener("keydown",c,{capture:!0})},[s,r])}var u1="DismissableLayer",Rs="dismissableLayer.update",c1="dismissableLayer.pointerDownOutside",r1="dismissableLayer.focusOutside",th,pv=b.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),bv=b.forwardRef((i,r)=>{const{disableOutsidePointerEvents:s=!1,onEscapeKeyDown:c,onPointerDownOutside:d,onFocusOutside:h,onInteractOutside:v,onDismiss:g,...x}=i,y=b.useContext(pv),[E,T]=b.useState(null),R=(E==null?void 0:E.ownerDocument)??(globalThis==null?void 0:globalThis.document),[,O]=b.useState({}),H=Ke(r,F=>T(F)),j=Array.from(y.layers),[z]=[...y.layersWithOutsidePointerEventsDisabled].slice(-1),V=j.indexOf(z),J=E?j.indexOf(E):-1,U=y.layersWithOutsidePointerEventsDisabled.size>0,X=J>=V,Z=f1(F=>{const L=F.target,ne=[...y.branches].some(re=>re.contains(L));!X||ne||(d==null||d(F),v==null||v(F),F.defaultPrevented||g==null||g())},R),le=d1(F=>{const L=F.target;[...y.branches].some(re=>re.contains(L))||(h==null||h(F),v==null||v(F),F.defaultPrevented||g==null||g())},R);return i1(F=>{J===y.layers.size-1&&(c==null||c(F),!F.defaultPrevented&&g&&(F.preventDefault(),g()))},R),b.useEffect(()=>{if(E)return s&&(y.layersWithOutsidePointerEventsDisabled.size===0&&(th=R.body.style.pointerEvents,R.body.style.pointerEvents="none"),y.layersWithOutsidePointerEventsDisabled.add(E)),y.layers.add(E),lh(),()=>{s&&y.layersWithOutsidePointerEventsDisabled.size===1&&(R.body.style.pointerEvents=th)}},[E,R,s,y]),b.useEffect(()=>()=>{E&&(y.layers.delete(E),y.layersWithOutsidePointerEventsDisabled.delete(E),lh())},[E,y]),b.useEffect(()=>{const F=()=>O({});return document.addEventListener(Rs,F),()=>document.removeEventListener(Rs,F)},[]),f.jsx(ze.div,{...x,ref:H,style:{pointerEvents:U?X?"auto":"none":void 0,...i.style},onFocusCapture:De(i.onFocusCapture,le.onFocusCapture),onBlurCapture:De(i.onBlurCapture,le.onBlurCapture),onPointerDownCapture:De(i.onPointerDownCapture,Z.onPointerDownCapture)})});bv.displayName=u1;var s1="DismissableLayerBranch",o1=b.forwardRef((i,r)=>{const s=b.useContext(pv),c=b.useRef(null),d=Ke(r,c);return b.useEffect(()=>{const h=c.current;if(h)return s.branches.add(h),()=>{s.branches.delete(h)}},[s.branches]),f.jsx(ze.div,{...i,ref:d})});o1.displayName=s1;function f1(i,r=globalThis==null?void 0:globalThis.document){const s=Ia(i),c=b.useRef(!1),d=b.useRef(()=>{});return b.useEffect(()=>{const h=g=>{if(g.target&&!c.current){let x=function(){xv(c1,s,y,{discrete:!0})};const y={originalEvent:g};g.pointerType==="touch"?(r.removeEventListener("click",d.current),d.current=x,r.addEventListener("click",d.current,{once:!0})):x()}else r.removeEventListener("click",d.current);c.current=!1},v=window.setTimeout(()=>{r.addEventListener("pointerdown",h)},0);return()=>{window.clearTimeout(v),r.removeEventListener("pointerdown",h),r.removeEventListener("click",d.current)}},[r,s]),{onPointerDownCapture:()=>c.current=!0}}function d1(i,r=globalThis==null?void 0:globalThis.document){const s=Ia(i),c=b.useRef(!1);return b.useEffect(()=>{const d=h=>{h.target&&!c.current&&xv(r1,s,{originalEvent:h},{discrete:!1})};return r.addEventListener("focusin",d),()=>r.removeEventListener("focusin",d)},[r,s]),{onFocusCapture:()=>c.current=!0,onBlurCapture:()=>c.current=!1}}function lh(){const i=new CustomEvent(Rs);document.dispatchEvent(i)}function xv(i,r,s,{discrete:c}){const d=s.originalEvent.target,h=new CustomEvent(i,{bubbles:!1,cancelable:!0,detail:s});r&&d.addEventListener(i,r,{once:!0}),c?Jy(d,h):d.dispatchEvent(h)}var ds="focusScope.autoFocusOnMount",ms="focusScope.autoFocusOnUnmount",ah={bubbles:!1,cancelable:!0},m1="FocusScope",Sv=b.forwardRef((i,r)=>{const{loop:s=!1,trapped:c=!1,onMountAutoFocus:d,onUnmountAutoFocus:h,...v}=i,[g,x]=b.useState(null),y=Ia(d),E=Ia(h),T=b.useRef(null),R=Ke(r,j=>x(j)),O=b.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;b.useEffect(()=>{if(c){let j=function(U){if(O.paused||!g)return;const X=U.target;g.contains(X)?T.current=X:ql(T.current,{select:!0})},z=function(U){if(O.paused||!g)return;const X=U.relatedTarget;X!==null&&(g.contains(X)||ql(T.current,{select:!0}))},V=function(U){if(document.activeElement===document.body)for(const Z of U)Z.removedNodes.length>0&&ql(g)};document.addEventListener("focusin",j),document.addEventListener("focusout",z);const J=new MutationObserver(V);return g&&J.observe(g,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",j),document.removeEventListener("focusout",z),J.disconnect()}}},[c,g,O.paused]),b.useEffect(()=>{if(g){ih.add(O);const j=document.activeElement;if(!g.contains(j)){const V=new CustomEvent(ds,ah);g.addEventListener(ds,y),g.dispatchEvent(V),V.defaultPrevented||(h1(b1(Ev(g)),{select:!0}),document.activeElement===j&&ql(g))}return()=>{g.removeEventListener(ds,y),setTimeout(()=>{const V=new CustomEvent(ms,ah);g.addEventListener(ms,E),g.dispatchEvent(V),V.defaultPrevented||ql(j??document.body,{select:!0}),g.removeEventListener(ms,E),ih.remove(O)},0)}}},[g,y,E,O]);const H=b.useCallback(j=>{if(!s&&!c||O.paused)return;const z=j.key==="Tab"&&!j.altKey&&!j.ctrlKey&&!j.metaKey,V=document.activeElement;if(z&&V){const J=j.currentTarget,[U,X]=v1(J);U&&X?!j.shiftKey&&V===X?(j.preventDefault(),s&&ql(U,{select:!0})):j.shiftKey&&V===U&&(j.preventDefault(),s&&ql(X,{select:!0})):V===J&&j.preventDefault()}},[s,c,O.paused]);return f.jsx(ze.div,{tabIndex:-1,...v,ref:R,onKeyDown:H})});Sv.displayName=m1;function h1(i,{select:r=!1}={}){const s=document.activeElement;for(const c of i)if(ql(c,{select:r}),document.activeElement!==s)return}function v1(i){const r=Ev(i),s=nh(r,i),c=nh(r.reverse(),i);return[s,c]}function Ev(i){const r=[],s=document.createTreeWalker(i,NodeFilter.SHOW_ELEMENT,{acceptNode:c=>{const d=c.tagName==="INPUT"&&c.type==="hidden";return c.disabled||c.hidden||d?NodeFilter.FILTER_SKIP:c.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;s.nextNode();)r.push(s.currentNode);return r}function nh(i,r){for(const s of i)if(!g1(s,{upTo:r}))return s}function g1(i,{upTo:r}){if(getComputedStyle(i).visibility==="hidden")return!0;for(;i;){if(r!==void 0&&i===r)return!1;if(getComputedStyle(i).display==="none")return!0;i=i.parentElement}return!1}function y1(i){return i instanceof HTMLInputElement&&"select"in i}function ql(i,{select:r=!1}={}){if(i&&i.focus){const s=document.activeElement;i.focus({preventScroll:!0}),i!==s&&y1(i)&&r&&i.select()}}var ih=p1();function p1(){let i=[];return{add(r){const s=i[0];r!==s&&(s==null||s.pause()),i=uh(i,r),i.unshift(r)},remove(r){var s;i=uh(i,r),(s=i[0])==null||s.resume()}}}function uh(i,r){const s=[...i],c=s.indexOf(r);return c!==-1&&s.splice(c,1),s}function b1(i){return i.filter(r=>r.tagName!=="A")}var x1="Portal",Nv=b.forwardRef((i,r)=>{var g;const{container:s,...c}=i,[d,h]=b.useState(!1);Pa(()=>h(!0),[]);const v=s||d&&((g=globalThis==null?void 0:globalThis.document)==null?void 0:g.body);return v?Zy.createPortal(f.jsx(ze.div,{...c,ref:r}),v):null});Nv.displayName=x1;var hs=0;function S1(){b.useEffect(()=>{const i=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",i[0]??ch()),document.body.insertAdjacentElement("beforeend",i[1]??ch()),hs++,()=>{hs===1&&document.querySelectorAll("[data-radix-focus-guard]").forEach(r=>r.remove()),hs--}},[])}function ch(){const i=document.createElement("span");return i.setAttribute("data-radix-focus-guard",""),i.tabIndex=0,i.style.outline="none",i.style.opacity="0",i.style.position="fixed",i.style.pointerEvents="none",i}var Zt=function(){return Zt=Object.assign||function(r){for(var s,c=1,d=arguments.length;c<d;c++){s=arguments[c];for(var h in s)Object.prototype.hasOwnProperty.call(s,h)&&(r[h]=s[h])}return r},Zt.apply(this,arguments)};function Tv(i,r){var s={};for(var c in i)Object.prototype.hasOwnProperty.call(i,c)&&r.indexOf(c)<0&&(s[c]=i[c]);if(i!=null&&typeof Object.getOwnPropertySymbols=="function")for(var d=0,c=Object.getOwnPropertySymbols(i);d<c.length;d++)r.indexOf(c[d])<0&&Object.prototype.propertyIsEnumerable.call(i,c[d])&&(s[c[d]]=i[c[d]]);return s}function E1(i,r,s){if(s||arguments.length===2)for(var c=0,d=r.length,h;c<d;c++)(h||!(c in r))&&(h||(h=Array.prototype.slice.call(r,0,c)),h[c]=r[c]);return i.concat(h||Array.prototype.slice.call(r))}var Ou="right-scroll-bar-position",zu="width-before-scroll-bar",N1="with-scroll-bars-hidden",T1="--removed-body-scroll-bar-size";function vs(i,r){return typeof i=="function"?i(r):i&&(i.current=r),i}function A1(i,r){var s=b.useState(function(){return{value:i,callback:r,facade:{get current(){return s.value},set current(c){var d=s.value;d!==c&&(s.value=c,s.callback(c,d))}}}})[0];return s.callback=r,s.facade}var j1=typeof window<"u"?b.useLayoutEffect:b.useEffect,rh=new WeakMap;function _1(i,r){var s=A1(null,function(c){return i.forEach(function(d){return vs(d,c)})});return j1(function(){var c=rh.get(s);if(c){var d=new Set(c),h=new Set(i),v=s.current;d.forEach(function(g){h.has(g)||vs(g,null)}),h.forEach(function(g){d.has(g)||vs(g,v)})}rh.set(s,i)},[i]),s}function M1(i){return i}function R1(i,r){r===void 0&&(r=M1);var s=[],c=!1,d={read:function(){if(c)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return s.length?s[s.length-1]:i},useMedium:function(h){var v=r(h,c);return s.push(v),function(){s=s.filter(function(g){return g!==v})}},assignSyncMedium:function(h){for(c=!0;s.length;){var v=s;s=[],v.forEach(h)}s={push:function(g){return h(g)},filter:function(){return s}}},assignMedium:function(h){c=!0;var v=[];if(s.length){var g=s;s=[],g.forEach(h),v=s}var x=function(){var E=v;v=[],E.forEach(h)},y=function(){return Promise.resolve().then(x)};y(),s={push:function(E){v.push(E),y()},filter:function(E){return v=v.filter(E),s}}}};return d}function w1(i){i===void 0&&(i={});var r=R1(null);return r.options=Zt({async:!0,ssr:!1},i),r}var Av=function(i){var r=i.sideCar,s=Tv(i,["sideCar"]);if(!r)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var c=r.read();if(!c)throw new Error("Sidecar medium not found");return b.createElement(c,Zt({},s))};Av.isSideCarExport=!0;function C1(i,r){return i.useMedium(r),Av}var jv=w1(),gs=function(){},qu=b.forwardRef(function(i,r){var s=b.useRef(null),c=b.useState({onScrollCapture:gs,onWheelCapture:gs,onTouchMoveCapture:gs}),d=c[0],h=c[1],v=i.forwardProps,g=i.children,x=i.className,y=i.removeScrollBar,E=i.enabled,T=i.shards,R=i.sideCar,O=i.noIsolation,H=i.inert,j=i.allowPinchZoom,z=i.as,V=z===void 0?"div":z,J=i.gapMode,U=Tv(i,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),X=R,Z=_1([s,r]),le=Zt(Zt({},U),d);return b.createElement(b.Fragment,null,E&&b.createElement(X,{sideCar:jv,removeScrollBar:y,shards:T,noIsolation:O,inert:H,setCallbacks:h,allowPinchZoom:!!j,lockRef:s,gapMode:J}),v?b.cloneElement(b.Children.only(g),Zt(Zt({},le),{ref:Z})):b.createElement(V,Zt({},le,{className:x,ref:Z}),g))});qu.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1};qu.classNames={fullWidth:zu,zeroRight:Ou};var D1=function(){if(typeof __webpack_nonce__<"u")return __webpack_nonce__};function O1(){if(!document)return null;var i=document.createElement("style");i.type="text/css";var r=D1();return r&&i.setAttribute("nonce",r),i}function z1(i,r){i.styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r))}function U1(i){var r=document.head||document.getElementsByTagName("head")[0];r.appendChild(i)}var H1=function(){var i=0,r=null;return{add:function(s){i==0&&(r=O1())&&(z1(r,s),U1(r)),i++},remove:function(){i--,!i&&r&&(r.parentNode&&r.parentNode.removeChild(r),r=null)}}},k1=function(){var i=H1();return function(r,s){b.useEffect(function(){return i.add(r),function(){i.remove()}},[r&&s])}},_v=function(){var i=k1(),r=function(s){var c=s.styles,d=s.dynamic;return i(c,d),null};return r},B1={left:0,top:0,right:0,gap:0},ys=function(i){return parseInt(i||"",10)||0},L1=function(i){var r=window.getComputedStyle(document.body),s=r[i==="padding"?"paddingLeft":"marginLeft"],c=r[i==="padding"?"paddingTop":"marginTop"],d=r[i==="padding"?"paddingRight":"marginRight"];return[ys(s),ys(c),ys(d)]},q1=function(i){if(i===void 0&&(i="margin"),typeof window>"u")return B1;var r=L1(i),s=document.documentElement.clientWidth,c=window.innerWidth;return{left:r[0],top:r[1],right:r[2],gap:Math.max(0,c-s+r[2]-r[0])}},G1=_v(),Fa="data-scroll-locked",Y1=function(i,r,s,c){var d=i.left,h=i.top,v=i.right,g=i.gap;return s===void 0&&(s="margin"),`
  .`.concat(N1,` {
   overflow: hidden `).concat(c,`;
   padding-right: `).concat(g,"px ").concat(c,`;
  }
  body[`).concat(Fa,`] {
    overflow: hidden `).concat(c,`;
    overscroll-behavior: contain;
    `).concat([r&&"position: relative ".concat(c,";"),s==="margin"&&`
    padding-left: `.concat(d,`px;
    padding-top: `).concat(h,`px;
    padding-right: `).concat(v,`px;
    margin-left:0;
    margin-top:0;
    margin-right: `).concat(g,"px ").concat(c,`;
    `),s==="padding"&&"padding-right: ".concat(g,"px ").concat(c,";")].filter(Boolean).join(""),`
  }
  
  .`).concat(Ou,` {
    right: `).concat(g,"px ").concat(c,`;
  }
  
  .`).concat(zu,` {
    margin-right: `).concat(g,"px ").concat(c,`;
  }
  
  .`).concat(Ou," .").concat(Ou,` {
    right: 0 `).concat(c,`;
  }
  
  .`).concat(zu," .").concat(zu,` {
    margin-right: 0 `).concat(c,`;
  }
  
  body[`).concat(Fa,`] {
    `).concat(T1,": ").concat(g,`px;
  }
`)},sh=function(){var i=parseInt(document.body.getAttribute(Fa)||"0",10);return isFinite(i)?i:0},V1=function(){b.useEffect(function(){return document.body.setAttribute(Fa,(sh()+1).toString()),function(){var i=sh()-1;i<=0?document.body.removeAttribute(Fa):document.body.setAttribute(Fa,i.toString())}},[])},X1=function(i){var r=i.noRelative,s=i.noImportant,c=i.gapMode,d=c===void 0?"margin":c;V1();var h=b.useMemo(function(){return q1(d)},[d]);return b.createElement(G1,{styles:Y1(h,!r,d,s?"":"!important")})},ws=!1;if(typeof window<"u")try{var Mu=Object.defineProperty({},"passive",{get:function(){return ws=!0,!0}});window.addEventListener("test",Mu,Mu),window.removeEventListener("test",Mu,Mu)}catch{ws=!1}var Ja=ws?{passive:!1}:!1,Q1=function(i){return i.tagName==="TEXTAREA"},Mv=function(i,r){if(!(i instanceof Element))return!1;var s=window.getComputedStyle(i);return s[r]!=="hidden"&&!(s.overflowY===s.overflowX&&!Q1(i)&&s[r]==="visible")},Z1=function(i){return Mv(i,"overflowY")},K1=function(i){return Mv(i,"overflowX")},oh=function(i,r){var s=r.ownerDocument,c=r;do{typeof ShadowRoot<"u"&&c instanceof ShadowRoot&&(c=c.host);var d=Rv(i,c);if(d){var h=wv(i,c),v=h[1],g=h[2];if(v>g)return!0}c=c.parentNode}while(c&&c!==s.body);return!1},J1=function(i){var r=i.scrollTop,s=i.scrollHeight,c=i.clientHeight;return[r,s,c]},$1=function(i){var r=i.scrollLeft,s=i.scrollWidth,c=i.clientWidth;return[r,s,c]},Rv=function(i,r){return i==="v"?Z1(r):K1(r)},wv=function(i,r){return i==="v"?J1(r):$1(r)},W1=function(i,r){return i==="h"&&r==="rtl"?-1:1},F1=function(i,r,s,c,d){var h=W1(i,window.getComputedStyle(r).direction),v=h*c,g=s.target,x=r.contains(g),y=!1,E=v>0,T=0,R=0;do{var O=wv(i,g),H=O[0],j=O[1],z=O[2],V=j-z-h*H;(H||V)&&Rv(i,g)&&(T+=V,R+=H),g instanceof ShadowRoot?g=g.host:g=g.parentNode}while(!x&&g!==document.body||x&&(r.contains(g)||r===g));return(E&&Math.abs(T)<1||!E&&Math.abs(R)<1)&&(y=!0),y},Ru=function(i){return"changedTouches"in i?[i.changedTouches[0].clientX,i.changedTouches[0].clientY]:[0,0]},fh=function(i){return[i.deltaX,i.deltaY]},dh=function(i){return i&&"current"in i?i.current:i},P1=function(i,r){return i[0]===r[0]&&i[1]===r[1]},I1=function(i){return`
  .block-interactivity-`.concat(i,` {pointer-events: none;}
  .allow-interactivity-`).concat(i,` {pointer-events: all;}
`)},ex=0,$a=[];function tx(i){var r=b.useRef([]),s=b.useRef([0,0]),c=b.useRef(),d=b.useState(ex++)[0],h=b.useState(_v)[0],v=b.useRef(i);b.useEffect(function(){v.current=i},[i]),b.useEffect(function(){if(i.inert){document.body.classList.add("block-interactivity-".concat(d));var j=E1([i.lockRef.current],(i.shards||[]).map(dh),!0).filter(Boolean);return j.forEach(function(z){return z.classList.add("allow-interactivity-".concat(d))}),function(){document.body.classList.remove("block-interactivity-".concat(d)),j.forEach(function(z){return z.classList.remove("allow-interactivity-".concat(d))})}}},[i.inert,i.lockRef.current,i.shards]);var g=b.useCallback(function(j,z){if("touches"in j&&j.touches.length===2||j.type==="wheel"&&j.ctrlKey)return!v.current.allowPinchZoom;var V=Ru(j),J=s.current,U="deltaX"in j?j.deltaX:J[0]-V[0],X="deltaY"in j?j.deltaY:J[1]-V[1],Z,le=j.target,F=Math.abs(U)>Math.abs(X)?"h":"v";if("touches"in j&&F==="h"&&le.type==="range")return!1;var L=oh(F,le);if(!L)return!0;if(L?Z=F:(Z=F==="v"?"h":"v",L=oh(F,le)),!L)return!1;if(!c.current&&"changedTouches"in j&&(U||X)&&(c.current=Z),!Z)return!0;var ne=c.current||Z;return F1(ne,z,j,ne==="h"?U:X)},[]),x=b.useCallback(function(j){var z=j;if(!(!$a.length||$a[$a.length-1]!==h)){var V="deltaY"in z?fh(z):Ru(z),J=r.current.filter(function(Z){return Z.name===z.type&&(Z.target===z.target||z.target===Z.shadowParent)&&P1(Z.delta,V)})[0];if(J&&J.should){z.cancelable&&z.preventDefault();return}if(!J){var U=(v.current.shards||[]).map(dh).filter(Boolean).filter(function(Z){return Z.contains(z.target)}),X=U.length>0?g(z,U[0]):!v.current.noIsolation;X&&z.cancelable&&z.preventDefault()}}},[]),y=b.useCallback(function(j,z,V,J){var U={name:j,delta:z,target:V,should:J,shadowParent:lx(V)};r.current.push(U),setTimeout(function(){r.current=r.current.filter(function(X){return X!==U})},1)},[]),E=b.useCallback(function(j){s.current=Ru(j),c.current=void 0},[]),T=b.useCallback(function(j){y(j.type,fh(j),j.target,g(j,i.lockRef.current))},[]),R=b.useCallback(function(j){y(j.type,Ru(j),j.target,g(j,i.lockRef.current))},[]);b.useEffect(function(){return $a.push(h),i.setCallbacks({onScrollCapture:T,onWheelCapture:T,onTouchMoveCapture:R}),document.addEventListener("wheel",x,Ja),document.addEventListener("touchmove",x,Ja),document.addEventListener("touchstart",E,Ja),function(){$a=$a.filter(function(j){return j!==h}),document.removeEventListener("wheel",x,Ja),document.removeEventListener("touchmove",x,Ja),document.removeEventListener("touchstart",E,Ja)}},[]);var O=i.removeScrollBar,H=i.inert;return b.createElement(b.Fragment,null,H?b.createElement(h,{styles:I1(d)}):null,O?b.createElement(X1,{gapMode:i.gapMode}):null)}function lx(i){for(var r=null;i!==null;)i instanceof ShadowRoot&&(r=i.host,i=i.host),i=i.parentNode;return r}const ax=C1(jv,tx);var Cv=b.forwardRef(function(i,r){return b.createElement(qu,Zt({},i,{ref:r,sideCar:ax}))});Cv.classNames=qu.classNames;var nx=function(i){if(typeof document>"u")return null;var r=Array.isArray(i)?i[0]:i;return r.ownerDocument.body},Wa=new WeakMap,wu=new WeakMap,Cu={},ps=0,Dv=function(i){return i&&(i.host||Dv(i.parentNode))},ix=function(i,r){return r.map(function(s){if(i.contains(s))return s;var c=Dv(s);return c&&i.contains(c)?c:(console.error("aria-hidden",s,"in not contained inside",i,". Doing nothing"),null)}).filter(function(s){return!!s})},ux=function(i,r,s,c){var d=ix(r,Array.isArray(i)?i:[i]);Cu[s]||(Cu[s]=new WeakMap);var h=Cu[s],v=[],g=new Set,x=new Set(d),y=function(T){!T||g.has(T)||(g.add(T),y(T.parentNode))};d.forEach(y);var E=function(T){!T||x.has(T)||Array.prototype.forEach.call(T.children,function(R){if(g.has(R))E(R);else try{var O=R.getAttribute(c),H=O!==null&&O!=="false",j=(Wa.get(R)||0)+1,z=(h.get(R)||0)+1;Wa.set(R,j),h.set(R,z),v.push(R),j===1&&H&&wu.set(R,!0),z===1&&R.setAttribute(s,"true"),H||R.setAttribute(c,"true")}catch(V){console.error("aria-hidden: cannot operate on ",R,V)}})};return E(r),g.clear(),ps++,function(){v.forEach(function(T){var R=Wa.get(T)-1,O=h.get(T)-1;Wa.set(T,R),h.set(T,O),R||(wu.has(T)||T.removeAttribute(c),wu.delete(T)),O||T.removeAttribute(s)}),ps--,ps||(Wa=new WeakMap,Wa=new WeakMap,wu=new WeakMap,Cu={})}},cx=function(i,r,s){s===void 0&&(s="data-aria-hidden");var c=Array.from(Array.isArray(i)?i:[i]),d=nx(i);return d?(c.push.apply(c,Array.from(d.querySelectorAll("[aria-live]"))),ux(c,d,s,"aria-hidden")):function(){return null}},Gu="Dialog",[Ov,Vx]=tn(Gu),[rx,Lt]=Ov(Gu),zv=i=>{const{__scopeDialog:r,children:s,open:c,defaultOpen:d,onOpenChange:h,modal:v=!0}=i,g=b.useRef(null),x=b.useRef(null),[y,E]=si({prop:c,defaultProp:d??!1,onChange:h,caller:Gu});return f.jsx(rx,{scope:r,triggerRef:g,contentRef:x,contentId:ci(),titleId:ci(),descriptionId:ci(),open:y,onOpenChange:E,onOpenToggle:b.useCallback(()=>E(T=>!T),[E]),modal:v,children:s})};zv.displayName=Gu;var Uv="DialogTrigger",Hv=b.forwardRef((i,r)=>{const{__scopeDialog:s,...c}=i,d=Lt(Uv,s),h=Ke(r,d.triggerRef);return f.jsx(ze.button,{type:"button","aria-haspopup":"dialog","aria-expanded":d.open,"aria-controls":d.contentId,"data-state":Bs(d.open),...c,ref:h,onClick:De(i.onClick,d.onOpenToggle)})});Hv.displayName=Uv;var Hs="DialogPortal",[sx,kv]=Ov(Hs,{forceMount:void 0}),Bv=i=>{const{__scopeDialog:r,forceMount:s,children:c,container:d}=i,h=Lt(Hs,r);return f.jsx(sx,{scope:r,forceMount:s,children:b.Children.map(c,v=>f.jsx(ln,{present:s||h.open,children:f.jsx(Nv,{asChild:!0,container:d,children:v})}))})};Bv.displayName=Hs;var Hu="DialogOverlay",Lv=b.forwardRef((i,r)=>{const s=kv(Hu,i.__scopeDialog),{forceMount:c=s.forceMount,...d}=i,h=Lt(Hu,i.__scopeDialog);return h.modal?f.jsx(ln,{present:c||h.open,children:f.jsx(fx,{...d,ref:r})}):null});Lv.displayName=Hu;var ox=ri("DialogOverlay.RemoveScroll"),fx=b.forwardRef((i,r)=>{const{__scopeDialog:s,...c}=i,d=Lt(Hu,s);return f.jsx(Cv,{as:ox,allowPinchZoom:!0,shards:[d.contentRef],children:f.jsx(ze.div,{"data-state":Bs(d.open),...c,ref:r,style:{pointerEvents:"auto",...c.style}})})}),ca="DialogContent",qv=b.forwardRef((i,r)=>{const s=kv(ca,i.__scopeDialog),{forceMount:c=s.forceMount,...d}=i,h=Lt(ca,i.__scopeDialog);return f.jsx(ln,{present:c||h.open,children:h.modal?f.jsx(dx,{...d,ref:r}):f.jsx(mx,{...d,ref:r})})});qv.displayName=ca;var dx=b.forwardRef((i,r)=>{const s=Lt(ca,i.__scopeDialog),c=b.useRef(null),d=Ke(r,s.contentRef,c);return b.useEffect(()=>{const h=c.current;if(h)return cx(h)},[]),f.jsx(Gv,{...i,ref:d,trapFocus:s.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:De(i.onCloseAutoFocus,h=>{var v;h.preventDefault(),(v=s.triggerRef.current)==null||v.focus()}),onPointerDownOutside:De(i.onPointerDownOutside,h=>{const v=h.detail.originalEvent,g=v.button===0&&v.ctrlKey===!0;(v.button===2||g)&&h.preventDefault()}),onFocusOutside:De(i.onFocusOutside,h=>h.preventDefault())})}),mx=b.forwardRef((i,r)=>{const s=Lt(ca,i.__scopeDialog),c=b.useRef(!1),d=b.useRef(!1);return f.jsx(Gv,{...i,ref:r,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:h=>{var v,g;(v=i.onCloseAutoFocus)==null||v.call(i,h),h.defaultPrevented||(c.current||(g=s.triggerRef.current)==null||g.focus(),h.preventDefault()),c.current=!1,d.current=!1},onInteractOutside:h=>{var x,y;(x=i.onInteractOutside)==null||x.call(i,h),h.defaultPrevented||(c.current=!0,h.detail.originalEvent.type==="pointerdown"&&(d.current=!0));const v=h.target;((y=s.triggerRef.current)==null?void 0:y.contains(v))&&h.preventDefault(),h.detail.originalEvent.type==="focusin"&&d.current&&h.preventDefault()}})}),Gv=b.forwardRef((i,r)=>{const{__scopeDialog:s,trapFocus:c,onOpenAutoFocus:d,onCloseAutoFocus:h,...v}=i,g=Lt(ca,s),x=b.useRef(null),y=Ke(r,x);return S1(),f.jsxs(f.Fragment,{children:[f.jsx(Sv,{asChild:!0,loop:!0,trapped:c,onMountAutoFocus:d,onUnmountAutoFocus:h,children:f.jsx(bv,{role:"dialog",id:g.contentId,"aria-describedby":g.descriptionId,"aria-labelledby":g.titleId,"data-state":Bs(g.open),...v,ref:y,onDismiss:()=>g.onOpenChange(!1)})}),f.jsxs(f.Fragment,{children:[f.jsx(hx,{titleId:g.titleId}),f.jsx(gx,{contentRef:x,descriptionId:g.descriptionId})]})]})}),ks="DialogTitle",Yv=b.forwardRef((i,r)=>{const{__scopeDialog:s,...c}=i,d=Lt(ks,s);return f.jsx(ze.h2,{id:d.titleId,...c,ref:r})});Yv.displayName=ks;var Vv="DialogDescription",Xv=b.forwardRef((i,r)=>{const{__scopeDialog:s,...c}=i,d=Lt(Vv,s);return f.jsx(ze.p,{id:d.descriptionId,...c,ref:r})});Xv.displayName=Vv;var Qv="DialogClose",Zv=b.forwardRef((i,r)=>{const{__scopeDialog:s,...c}=i,d=Lt(Qv,s);return f.jsx(ze.button,{type:"button",...c,ref:r,onClick:De(i.onClick,()=>d.onOpenChange(!1))})});Zv.displayName=Qv;function Bs(i){return i?"open":"closed"}var Kv="DialogTitleWarning",[Xx,Jv]=Hy(Kv,{contentName:ca,titleName:ks,docsSlug:"dialog"}),hx=({titleId:i})=>{const r=Jv(Kv),s=`\`${r.contentName}\` requires a \`${r.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${r.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${r.docsSlug}`;return b.useEffect(()=>{i&&(document.getElementById(i)||console.error(s))},[s,i]),null},vx="DialogDescriptionWarning",gx=({contentRef:i,descriptionId:r})=>{const c=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${Jv(vx).contentName}}.`;return b.useEffect(()=>{var h;const d=(h=i.current)==null?void 0:h.getAttribute("aria-describedby");r&&d&&(document.getElementById(r)||console.warn(c))},[c,i,r]),null},yx=zv,px=Hv,bx=Bv,xx=Lv,Sx=qv,Ex=Yv,Nx=Xv,Tx=Zv;function Ax({...i}){return f.jsx(yx,{"data-slot":"dialog",...i})}function jx({...i}){return f.jsx(px,{"data-slot":"dialog-trigger",...i})}function _x({...i}){return f.jsx(bx,{"data-slot":"dialog-portal",...i})}function Mx({className:i,...r}){return f.jsx(xx,{"data-slot":"dialog-overlay",className:He("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",i),...r})}function Rx({className:i,children:r,...s}){return f.jsxs(_x,{"data-slot":"dialog-portal",children:[f.jsx(Mx,{}),f.jsxs(Sx,{"data-slot":"dialog-content",className:He("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg",i),...s,children:[r,f.jsxs(Tx,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",children:[f.jsx(Uu,{}),f.jsx("span",{className:"sr-only",children:"Close"})]})]})]})}function wx({className:i,...r}){return f.jsx("div",{"data-slot":"dialog-header",className:He("flex flex-col gap-2 text-center sm:text-left",i),...r})}function Cx({className:i,...r}){return f.jsx(Ex,{"data-slot":"dialog-title",className:He("text-lg leading-none font-semibold",i),...r})}function Dx({className:i,...r}){return f.jsx(Nx,{"data-slot":"dialog-description",className:He("text-muted-foreground text-sm",i),...r})}var Ox="Label",$v=b.forwardRef((i,r)=>f.jsx(ze.label,{...i,ref:r,onMouseDown:s=>{var d;s.target.closest("button, input, select, textarea")||((d=i.onMouseDown)==null||d.call(i,s),!s.defaultPrevented&&s.detail>1&&s.preventDefault())}}));$v.displayName=Ox;var zx=$v;function Du({className:i,...r}){return f.jsx(zx,{"data-slot":"label",className:He("flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50",i),...r})}var Yu="Checkbox",[Ux,Qx]=tn(Yu),[Hx,Ls]=Ux(Yu);function kx(i){const{__scopeCheckbox:r,checked:s,children:c,defaultChecked:d,disabled:h,form:v,name:g,onCheckedChange:x,required:y,value:E="on",internal_do_not_use_render:T}=i,[R,O]=si({prop:s,defaultProp:d??!1,onChange:x,caller:Yu}),[H,j]=b.useState(null),[z,V]=b.useState(null),J=b.useRef(!1),U=H?!!v||!!H.closest("form"):!0,X={checked:R,disabled:h,setChecked:O,control:H,setControl:j,name:g,form:v,value:E,hasConsumerStoppedPropagationRef:J,required:y,defaultChecked:Gl(d)?!1:d,isFormControl:U,bubbleInput:z,setBubbleInput:V};return f.jsx(Hx,{scope:r,...X,children:Bx(T)?T(X):c})}var Wv="CheckboxTrigger",Fv=b.forwardRef(({__scopeCheckbox:i,onKeyDown:r,onClick:s,...c},d)=>{const{control:h,value:v,disabled:g,checked:x,required:y,setControl:E,setChecked:T,hasConsumerStoppedPropagationRef:R,isFormControl:O,bubbleInput:H}=Ls(Wv,i),j=Ke(d,E),z=b.useRef(x);return b.useEffect(()=>{const V=h==null?void 0:h.form;if(V){const J=()=>T(z.current);return V.addEventListener("reset",J),()=>V.removeEventListener("reset",J)}},[h,T]),f.jsx(ze.button,{type:"button",role:"checkbox","aria-checked":Gl(x)?"mixed":x,"aria-required":y,"data-state":ag(x),"data-disabled":g?"":void 0,disabled:g,value:v,...c,ref:j,onKeyDown:De(r,V=>{V.key==="Enter"&&V.preventDefault()}),onClick:De(s,V=>{T(J=>Gl(J)?!0:!J),H&&O&&(R.current=V.isPropagationStopped(),R.current||V.stopPropagation())})})});Fv.displayName=Wv;var Pv=b.forwardRef((i,r)=>{const{__scopeCheckbox:s,name:c,checked:d,defaultChecked:h,required:v,disabled:g,value:x,onCheckedChange:y,form:E,...T}=i;return f.jsx(kx,{__scopeCheckbox:s,checked:d,defaultChecked:h,disabled:g,required:v,onCheckedChange:y,name:c,form:E,value:x,internal_do_not_use_render:({isFormControl:R})=>f.jsxs(f.Fragment,{children:[f.jsx(Fv,{...T,ref:r,__scopeCheckbox:s}),R&&f.jsx(lg,{__scopeCheckbox:s})]})})});Pv.displayName=Yu;var Iv="CheckboxIndicator",eg=b.forwardRef((i,r)=>{const{__scopeCheckbox:s,forceMount:c,...d}=i,h=Ls(Iv,s);return f.jsx(ln,{present:c||Gl(h.checked)||h.checked===!0,children:f.jsx(ze.span,{"data-state":ag(h.checked),"data-disabled":h.disabled?"":void 0,...d,ref:r,style:{pointerEvents:"none",...i.style}})})});eg.displayName=Iv;var tg="CheckboxBubbleInput",lg=b.forwardRef(({__scopeCheckbox:i,...r},s)=>{const{control:c,hasConsumerStoppedPropagationRef:d,checked:h,defaultChecked:v,required:g,disabled:x,name:y,value:E,form:T,bubbleInput:R,setBubbleInput:O}=Ls(tg,i),H=Ke(s,O),j=lv(h),z=av(c);b.useEffect(()=>{const J=R;if(!J)return;const U=window.HTMLInputElement.prototype,Z=Object.getOwnPropertyDescriptor(U,"checked").set,le=!d.current;if(j!==h&&Z){const F=new Event("click",{bubbles:le});J.indeterminate=Gl(h),Z.call(J,Gl(h)?!1:h),J.dispatchEvent(F)}},[R,j,h,d]);const V=b.useRef(Gl(h)?!1:h);return f.jsx(ze.input,{type:"checkbox","aria-hidden":!0,defaultChecked:v??V.current,required:g,disabled:x,name:y,value:E,form:T,...r,tabIndex:-1,ref:H,style:{...r.style,...z,position:"absolute",pointerEvents:"none",opacity:0,margin:0,transform:"translateX(-100%)"}})});lg.displayName=tg;function Bx(i){return typeof i=="function"}function Gl(i){return i==="indeterminate"}function ag(i){return Gl(i)?"indeterminate":i?"checked":"unchecked"}function mh({className:i,...r}){return f.jsx(Pv,{"data-slot":"checkbox",className:He("peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50",i),...r,children:f.jsx(eg,{"data-slot":"checkbox-indicator",className:"flex items-center justify-center text-current transition-none",children:f.jsx(db,{className:"size-3.5"})})})}const Lx=({onImportComplete:i})=>{const[r,s]=b.useState(!1),[c,d]=b.useState([""]),[h,v]=b.useState([""]),[g,x]=b.useState(!0),[y,E]=b.useState(!0),[T,R]=b.useState(!1),[O,H]=b.useState(null),j=()=>{d([...c,""])},z=L=>{d(c.filter((ne,re)=>re!==L))},V=(L,ne)=>{const re=[...c];re[L]=ne,d(re)},J=()=>{v([...h,""])},U=L=>{v(h.filter((ne,re)=>re!==L))},X=(L,ne)=>{const re=[...h];re[L]=ne,v(re)},Z=async()=>{R(!0),H(null);try{const L=c.filter(se=>se.trim()!==""),ne=h.filter(se=>se.trim()!=="");if(L.length===0&&ne.length===0){alert("Please specify at least one file or directory to import"),R(!1);return}const re=await fetch("/api/notes/import",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({file_paths:L,directory_paths:ne,process_with_llm:g,recursive:y})});if(re.ok){const se=await re.json();H(se),i&&i(se)}else{const se=await re.json();alert(`Import failed: ${se.error}`)}}catch(L){console.error("Import error:",L),alert("Import failed: Network error")}finally{R(!1)}},le=()=>{d([""]),v([""]),x(!0),E(!0),H(null)},F=()=>{s(!1),setTimeout(le,300)};return f.jsxs(Ax,{open:r,onOpenChange:s,children:[f.jsx(jx,{asChild:!0,children:f.jsxs(lt,{children:[f.jsx(fs,{className:"h-4 w-4 mr-2"}),"Import Notes"]})}),f.jsxs(Rx,{className:"max-w-2xl max-h-[80vh] overflow-y-auto",children:[f.jsxs(wx,{children:[f.jsxs(Cx,{className:"flex items-center gap-2",children:[f.jsx(fs,{className:"h-5 w-5"}),"Import Notes and Files"]}),f.jsx(Dx,{children:"Import individual files or entire directories. Supports .md and .json files."})]}),O?f.jsxs("div",{className:"space-y-4",children:[f.jsxs("div",{className:"flex items-center gap-2 text-green-600",children:[f.jsx(hb,{className:"h-5 w-5"}),f.jsx("span",{className:"font-medium",children:"Import Completed"})]}),f.jsxs(rt,{children:[f.jsxs(kt,{children:[f.jsx(Bt,{className:"text-base",children:"Import Summary"}),f.jsxs(en,{children:["Successfully imported ",O.count," ",O.count===1?"note":"notes"]})]}),f.jsx(Tt,{children:f.jsx("div",{className:"space-y-3 max-h-60 overflow-y-auto",children:O.imported_notes.map(L=>f.jsx("div",{className:"flex items-center justify-between p-3 border rounded-lg",children:f.jsxs("div",{className:"flex-1",children:[f.jsx("h4",{className:"font-medium text-sm",children:L.title}),f.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[f.jsx(Kt,{variant:"outline",className:"text-xs",children:L.file_type.toUpperCase()}),L.tags&&L.tags.length>0&&f.jsxs("span",{className:"text-xs text-muted-foreground",children:[L.tags.length," tags"]}),L.topics&&L.topics.length>0&&f.jsxs("span",{className:"text-xs text-muted-foreground",children:[L.topics.length," topics"]})]})]})},L.id))})})]}),f.jsxs("div",{className:"flex justify-end gap-2",children:[f.jsx(lt,{variant:"outline",onClick:le,children:"Import More"}),f.jsx(lt,{onClick:F,children:"Done"})]})]}):f.jsxs("div",{className:"space-y-6",children:[f.jsxs("div",{className:"space-y-3",children:[f.jsxs(Du,{className:"text-base font-medium flex items-center gap-2",children:[f.jsx(ol,{className:"h-4 w-4"}),"Individual Files"]}),f.jsx("p",{className:"text-sm text-muted-foreground",children:"Specify full paths to individual .md or .json files"}),c.map((L,ne)=>f.jsxs("div",{className:"flex gap-2",children:[f.jsx(As,{placeholder:"/path/to/your/note.md",value:L,onChange:re=>V(ne,re.target.value),className:"flex-1"}),c.length>1&&f.jsx(lt,{variant:"outline",size:"sm",onClick:()=>z(ne),children:f.jsx(Uu,{className:"h-4 w-4"})})]},ne)),f.jsxs(lt,{variant:"outline",size:"sm",onClick:j,className:"w-full",children:[f.jsx(eh,{className:"h-4 w-4 mr-2"}),"Add Another File"]})]}),f.jsxs("div",{className:"space-y-3",children:[f.jsxs(Du,{className:"text-base font-medium flex items-center gap-2",children:[f.jsx(bb,{className:"h-4 w-4"}),"Directories"]}),f.jsx("p",{className:"text-sm text-muted-foreground",children:"Specify directories containing .md or .json files"}),h.map((L,ne)=>f.jsxs("div",{className:"flex gap-2",children:[f.jsx(As,{placeholder:"/path/to/your/notes/directory",value:L,onChange:re=>X(ne,re.target.value),className:"flex-1"}),h.length>1&&f.jsx(lt,{variant:"outline",size:"sm",onClick:()=>U(ne),children:f.jsx(Uu,{className:"h-4 w-4"})})]},ne)),f.jsxs(lt,{variant:"outline",size:"sm",onClick:J,className:"w-full",children:[f.jsx(eh,{className:"h-4 w-4 mr-2"}),"Add Another Directory"]})]}),f.jsxs(rt,{children:[f.jsx(kt,{className:"pb-3",children:f.jsx(Bt,{className:"text-base",children:"Import Options"})}),f.jsxs(Tt,{className:"space-y-4",children:[f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx(mh,{id:"recursive",checked:y,onCheckedChange:E}),f.jsx(Du,{htmlFor:"recursive",className:"text-sm",children:"Search subdirectories recursively"})]}),f.jsxs("div",{className:"flex items-center space-x-2",children:[f.jsx(mh,{id:"llm-processing",checked:g,onCheckedChange:x}),f.jsxs(Du,{htmlFor:"llm-processing",className:"text-sm flex items-center gap-2",children:[f.jsx(ev,{className:"h-4 w-4"}),"Process with LLM (extract topics, generate tags, create summaries)"]})]})]})]}),f.jsxs("div",{className:"flex justify-end gap-2",children:[f.jsx(lt,{variant:"outline",onClick:F,children:"Cancel"}),f.jsx(lt,{onClick:Z,disabled:T,children:T?f.jsxs(f.Fragment,{children:[f.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Importing..."]}):f.jsxs(f.Fragment,{children:[f.jsx(fs,{className:"h-4 w-4 mr-2"}),"Import"]})})]})]})]})]})};function qx(){const[i,r]=b.useState(null),[s,c]=b.useState("notes"),[d,h]=b.useState({totalNotes:0,totalTags:0,llmConnected:!1});b.useEffect(()=>{v()},[]);const v=async()=>{var T;try{const O=await(await fetch("/api/notes?per_page=1")).json(),j=await(await fetch("/api/tags")).json(),V=await(await fetch("/api/llm/test")).json();h({totalNotes:O.total||0,totalTags:((T=j.tags)==null?void 0:T.length)||0,llmConnected:V.connected||!1})}catch(R){console.error("Error fetching stats:",R)}},g=T=>{r(T),s==="graph"&&c("notes")},x=T=>{r(T),v()},y=()=>{v(),s==="notes"&&window.location.reload()},E=async T=>{try{const O=await(await fetch(`/api/notes/${T.id}`)).json();r(O),c("notes")}catch(R){console.error("Error fetching note:",R)}};return f.jsxs("div",{className:"min-h-screen bg-background",children:[f.jsx("header",{className:"border-b bg-card",children:f.jsx("div",{className:"container mx-auto px-4 py-4",children:f.jsxs("div",{className:"flex items-center justify-between",children:[f.jsxs("div",{className:"flex items-center gap-3",children:[f.jsxs("div",{className:"flex items-center gap-2",children:[f.jsx(Wh,{className:"h-8 w-8 text-primary"}),f.jsx("h1",{className:"text-2xl font-bold",children:"Note Manager"})]}),f.jsx(Kt,{variant:"outline",className:"text-xs",children:"Local Knowledge Management"})]}),f.jsxs("div",{className:"flex items-center gap-4",children:[f.jsxs("div",{className:"flex items-center gap-4 text-sm text-muted-foreground",children:[f.jsxs("div",{className:"flex items-center gap-1",children:[f.jsx(ol,{className:"h-4 w-4"}),f.jsxs("span",{children:[d.totalNotes," notes"]})]}),f.jsxs("div",{className:"flex items-center gap-1",children:[f.jsx(Bu,{className:"h-4 w-4"}),f.jsxs("span",{children:[d.totalTags," tags"]})]}),f.jsxs("div",{className:"flex items-center gap-1",children:[f.jsx("div",{className:`h-2 w-2 rounded-full ${d.llmConnected?"bg-green-500":"bg-red-500"}`}),f.jsxs("span",{children:["LLM ",d.llmConnected?"Connected":"Disconnected"]})]})]}),f.jsx(Lx,{onImportComplete:y}),f.jsxs(lt,{variant:"outline",size:"sm",children:[f.jsx(Ih,{className:"h-4 w-4 mr-2"}),"Settings"]})]})]})})}),f.jsx("main",{className:"container mx-auto px-4 py-6",children:f.jsxs(eb,{value:s,onValueChange:c,className:"h-[calc(100vh-140px)]",children:[f.jsxs(tb,{className:"grid w-full grid-cols-2 mb-6",children:[f.jsxs($m,{value:"notes",className:"flex items-center gap-2",children:[f.jsx(ol,{className:"h-4 w-4"}),"Notes"]}),f.jsxs($m,{value:"graph",className:"flex items-center gap-2",children:[f.jsx(Ts,{className:"h-4 w-4"}),"Graph View"]})]}),f.jsx(Wm,{value:"notes",className:"h-full",children:f.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6 h-full",children:[f.jsx("div",{className:"lg:col-span-1",children:f.jsxs(rt,{className:"h-full",children:[f.jsxs(kt,{children:[f.jsxs(Bt,{className:"flex items-center gap-2",children:[f.jsx(Ph,{className:"h-5 w-5"}),"Notes"]}),f.jsx(en,{children:"Browse and search your notes"})]}),f.jsx(Tt,{className:"h-[calc(100%-120px)] overflow-hidden",children:f.jsx(Ub,{onNoteSelect:g,selectedNoteId:i==null?void 0:i.id})})]})}),f.jsx("div",{className:"lg:col-span-2",children:f.jsx(rt,{className:"h-full",children:f.jsx(Tt,{className:"p-6 h-full",children:f.jsx(kb,{note:i,onNoteUpdate:x})})})})]})}),f.jsx(Wm,{value:"graph",className:"h-full",children:f.jsx(rt,{className:"h-full",children:f.jsx(Tt,{className:"p-6 h-full",children:f.jsx(n1,{onNodeSelect:E})})})})]})})]})}Uy.createRoot(document.getElementById("root")).render(f.jsx(b.StrictMode,{children:f.jsx(qx,{})}));
