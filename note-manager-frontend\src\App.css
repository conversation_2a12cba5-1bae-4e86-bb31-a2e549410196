@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);
}

:root {
  --radius: 0.75rem;

  /* Modern color palette with blue/purple theme */
  --background: oklch(0.99 0.005 240);
  --foreground: oklch(0.15 0.02 240);

  /* Card colors with subtle tints */
  --card: oklch(1 0.01 240);
  --card-foreground: oklch(0.15 0.02 240);

  /* Popover colors */
  --popover: oklch(1 0.01 240);
  --popover-foreground: oklch(0.15 0.02 240);

  /* Primary brand colors - vibrant blue */
  --primary: oklch(0.55 0.18 250);
  --primary-foreground: oklch(0.98 0.01 240);

  /* Secondary colors - soft purple */
  --secondary: oklch(0.95 0.03 280);
  --secondary-foreground: oklch(0.25 0.05 280);

  /* Muted colors */
  --muted: oklch(0.96 0.02 240);
  --muted-foreground: oklch(0.45 0.03 240);

  /* Accent colors - vibrant purple */
  --accent: oklch(0.92 0.05 280);
  --accent-foreground: oklch(0.25 0.05 280);

  /* Destructive colors */
  --destructive: oklch(0.65 0.25 15);

  /* Border and input colors */
  --border: oklch(0.9 0.02 240);
  --input: oklch(0.94 0.02 240);
  --ring: oklch(0.55 0.18 250);

  /* Chart colors - vibrant and distinct */
  --chart-1: oklch(0.6 0.2 250);
  --chart-2: oklch(0.65 0.15 180);
  --chart-3: oklch(0.7 0.12 120);
  --chart-4: oklch(0.75 0.18 60);
  --chart-5: oklch(0.8 0.15 300);

  /* Sidebar colors */
  --sidebar: oklch(0.98 0.01 240);
  --sidebar-foreground: oklch(0.15 0.02 240);
  --sidebar-primary: oklch(0.55 0.18 250);
  --sidebar-primary-foreground: oklch(0.98 0.01 240);
  --sidebar-accent: oklch(0.92 0.05 280);
  --sidebar-accent-foreground: oklch(0.25 0.05 280);
  --sidebar-border: oklch(0.9 0.02 240);
  --sidebar-ring: oklch(0.55 0.18 250);

  /* Custom gradient variables */
  --gradient-primary: linear-gradient(135deg, oklch(0.55 0.18 250) 0%, oklch(0.6 0.15 280) 100%);
  --gradient-secondary: linear-gradient(135deg, oklch(0.95 0.03 280) 0%, oklch(0.92 0.05 260) 100%);
  --gradient-accent: linear-gradient(135deg, oklch(0.92 0.05 280) 0%, oklch(0.88 0.08 300) 100%);
}

.dark {
  /* Dark mode with blue/purple theme */
  --background: oklch(0.08 0.02 240);
  --foreground: oklch(0.95 0.01 240);

  /* Card colors */
  --card: oklch(0.12 0.03 240);
  --card-foreground: oklch(0.95 0.01 240);

  /* Popover colors */
  --popover: oklch(0.12 0.03 240);
  --popover-foreground: oklch(0.95 0.01 240);

  /* Primary colors - brighter in dark mode */
  --primary: oklch(0.7 0.2 250);
  --primary-foreground: oklch(0.08 0.02 240);

  /* Secondary colors */
  --secondary: oklch(0.18 0.04 280);
  --secondary-foreground: oklch(0.9 0.02 280);

  /* Muted colors */
  --muted: oklch(0.15 0.03 240);
  --muted-foreground: oklch(0.65 0.02 240);

  /* Accent colors */
  --accent: oklch(0.2 0.06 280);
  --accent-foreground: oklch(0.9 0.02 280);

  /* Destructive colors */
  --destructive: oklch(0.7 0.25 15);

  /* Border and input colors */
  --border: oklch(0.25 0.03 240);
  --input: oklch(0.2 0.04 240);
  --ring: oklch(0.7 0.2 250);

  /* Chart colors for dark mode */
  --chart-1: oklch(0.65 0.22 250);
  --chart-2: oklch(0.7 0.18 180);
  --chart-3: oklch(0.75 0.15 120);
  --chart-4: oklch(0.8 0.2 60);
  --chart-5: oklch(0.85 0.18 300);

  /* Sidebar colors */
  --sidebar: oklch(0.1 0.02 240);
  --sidebar-foreground: oklch(0.95 0.01 240);
  --sidebar-primary: oklch(0.7 0.2 250);
  --sidebar-primary-foreground: oklch(0.08 0.02 240);
  --sidebar-accent: oklch(0.2 0.06 280);
  --sidebar-accent-foreground: oklch(0.9 0.02 280);
  --sidebar-border: oklch(0.25 0.03 240);
  --sidebar-ring: oklch(0.7 0.2 250);

  /* Update gradient variables for dark mode */
  --gradient-primary: linear-gradient(135deg, oklch(0.7 0.2 250) 0%, oklch(0.75 0.18 280) 100%);
  --gradient-secondary: linear-gradient(135deg, oklch(0.18 0.04 280) 0%, oklch(0.22 0.06 260) 100%);
  --gradient-accent: linear-gradient(135deg, oklch(0.2 0.06 280) 0%, oklch(0.25 0.08 300) 100%);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground antialiased;
  }
}

@layer components {
  /* Gradient backgrounds */
  .bg-gradient-primary {
    background: var(--gradient-primary);
  }

  .bg-gradient-secondary {
    background: var(--gradient-secondary);
  }

  .bg-gradient-accent {
    background: var(--gradient-accent);
  }

  /* Enhanced card styles */
  .card-enhanced {
    @apply bg-card border border-border/50 shadow-sm hover:shadow-md transition-all duration-200;
    backdrop-filter: blur(8px);
  }

  .card-enhanced:hover {
    @apply shadow-lg border-border;
    transform: translateY(-1px);
  }

  /* Glass effect */
  .glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  /* Enhanced button styles */
  .btn-gradient {
    @apply bg-gradient-primary text-primary-foreground hover:opacity-90 transition-all duration-200;
  }

  /* Text gradients */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced scrollbar */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: oklch(0.7 0.1 240) transparent;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: transparent;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: oklch(0.7 0.1 240);
    border-radius: 3px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: oklch(0.6 0.15 240);
  }
}
