import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
    Brain,
    Database,
    FileText,
    Network,
    Search,
    Settings,
    Tag
} from 'lucide-react'
import { useEffect, useState } from 'react'
import './App.css'
import GraphView from './components/GraphView'
import ImportDialog from './components/ImportDialog'
import NoteList from './components/NoteList'
import NoteViewer from './components/NoteViewer'

function App() {
  const [selectedNote, setSelectedNote] = useState(null)
  const [activeTab, setActiveTab] = useState('notes')
  const [stats, setStats] = useState({
    totalNotes: 0,
    totalTags: 0,
    llmConnected: false
  })

  useEffect(() => {
    fetchStats()
  }, [])

  const fetchStats = async () => {
    try {
      // Fetch notes count
      const notesResponse = await fetch('/api/notes?per_page=1')
      const notesData = await notesResponse.json()
      
      // Fetch tags count
      const tagsResponse = await fetch('/api/tags')
      const tagsData = await tagsResponse.json()
      
      // Test LLM connection
      const llmResponse = await fetch('/api/llm/test')
      const llmData = await llmResponse.json()

      setStats({
        totalNotes: notesData.total || 0,
        totalTags: tagsData.tags?.length || 0,
        llmConnected: llmData.connected || false
      })
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const handleNoteSelect = (note) => {
    setSelectedNote(note)
    if (activeTab === 'graph') {
      setActiveTab('notes')
    }
  }

  const handleNoteUpdate = (updatedNote) => {
    setSelectedNote(updatedNote)
    fetchStats() // Refresh stats in case tags changed
  }

  const handleImportComplete = () => {
    fetchStats()
    // Refresh note list if we're on the notes tab
    if (activeTab === 'notes') {
      window.location.reload() // Simple refresh for now
    }
  }

  const handleGraphNodeSelect = async (node) => {
    try {
      const response = await fetch(`/api/notes/${node.id}`)
      const noteData = await response.json()
      setSelectedNote(noteData)
      setActiveTab('notes')
    } catch (error) {
      console.error('Error fetching note:', error)
    }
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Header */}
      <header className="relative border-b border-border/50 bg-gradient-primary shadow-lg">
        <div className="absolute inset-0 bg-gradient-to-r from-primary/10 via-transparent to-accent/10"></div>
        <div className="relative container mx-auto px-6 py-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-3">
                <div className="relative">
                  <div className="absolute inset-0 bg-white/20 rounded-xl blur-sm"></div>
                  <Brain className="relative h-10 w-10 text-white drop-shadow-lg" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-white drop-shadow-sm">Note Manager</h1>
                  <p className="text-white/80 text-sm font-medium">Intelligent Knowledge Management</p>
                </div>
              </div>
              <Badge variant="secondary" className="bg-white/20 text-white border-white/30 backdrop-blur-sm">
                <Database className="h-3 w-3 mr-1" />
                Local Storage
              </Badge>
            </div>

            <div className="flex items-center gap-6">
              {/* Enhanced Stats */}
              <div className="hidden md:flex items-center gap-6">
                <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2">
                  <FileText className="h-4 w-4 text-white/90" />
                  <span className="text-white font-medium">{stats.totalNotes}</span>
                  <span className="text-white/70 text-sm">notes</span>
                </div>
                <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2">
                  <Tag className="h-4 w-4 text-white/90" />
                  <span className="text-white font-medium">{stats.totalTags}</span>
                  <span className="text-white/70 text-sm">tags</span>
                </div>
                <div className="flex items-center gap-2 bg-white/10 backdrop-blur-sm rounded-lg px-3 py-2">
                  <div className={`h-2 w-2 rounded-full ${stats.llmConnected ? 'bg-green-400 shadow-green-400/50 shadow-lg' : 'bg-red-400 shadow-red-400/50 shadow-lg'}`}></div>
                  <span className="text-white/70 text-sm">LLM</span>
                  <span className={`text-sm font-medium ${stats.llmConnected ? 'text-green-200' : 'text-red-200'}`}>
                    {stats.llmConnected ? 'Online' : 'Offline'}
                  </span>
                </div>
              </div>

              <div className="flex items-center gap-3">
                <ImportDialog onImportComplete={handleImportComplete} />

                <Button variant="secondary" size="sm" className="bg-white/20 hover:bg-white/30 text-white border-white/30 backdrop-blur-sm transition-all duration-200">
                  <Settings className="h-4 w-4 mr-2" />
                  Settings
                </Button>
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="container mx-auto px-6 py-8">
        <Tabs value={activeTab} onValueChange={setActiveTab} className="h-[calc(100vh-180px)]">
          <TabsList className="grid w-full max-w-md mx-auto grid-cols-2 mb-8 bg-muted/50 backdrop-blur-sm p-1 rounded-xl">
            <TabsTrigger
              value="notes"
              className="flex items-center gap-2 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
            >
              <FileText className="h-4 w-4" />
              <span className="font-medium">Notes</span>
            </TabsTrigger>
            <TabsTrigger
              value="graph"
              className="flex items-center gap-2 rounded-lg data-[state=active]:bg-white data-[state=active]:shadow-sm transition-all duration-200"
            >
              <Network className="h-4 w-4" />
              <span className="font-medium">Graph View</span>
            </TabsTrigger>
          </TabsList>

          <TabsContent value="notes" className="h-full">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 h-full">
              {/* Notes List */}
              <div className="lg:col-span-1">
                <Card className="h-full card-enhanced border-0 shadow-xl">
                  <CardHeader className="bg-gradient-secondary rounded-t-xl">
                    <CardTitle className="flex items-center gap-3 text-lg">
                      <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                        <Search className="h-5 w-5 text-primary" />
                      </div>
                      <span className="text-gradient">Your Notes</span>
                    </CardTitle>
                    <CardDescription className="text-muted-foreground/80">
                      Discover and explore your knowledge base
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="h-[calc(100%-140px)] overflow-hidden p-0">
                    <div className="p-6 h-full">
                      <NoteList
                        onNoteSelect={handleNoteSelect}
                        selectedNoteId={selectedNote?.id}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>

              {/* Note Viewer */}
              <div className="lg:col-span-2">
                <Card className="h-full card-enhanced border-0 shadow-xl">
                  <CardContent className="p-0 h-full">
                    <div className="p-8 h-full">
                      <NoteViewer
                        note={selectedNote}
                        onNoteUpdate={handleNoteUpdate}
                      />
                    </div>
                  </CardContent>
                </Card>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="graph" className="h-full">
            <Card className="h-full card-enhanced border-0 shadow-xl">
              <CardHeader className="bg-gradient-accent rounded-t-xl">
                <CardTitle className="flex items-center gap-3 text-lg">
                  <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                    <Network className="h-5 w-5 text-primary" />
                  </div>
                  <span className="text-gradient">Knowledge Graph</span>
                </CardTitle>
                <CardDescription className="text-muted-foreground/80">
                  Visualize connections between your notes
                </CardDescription>
              </CardHeader>
              <CardContent className="p-0 h-[calc(100%-140px)]">
                <div className="p-8 h-full">
                  <GraphView onNodeSelect={handleGraphNodeSelect} />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </main>
    </div>
  )
}

export default App
