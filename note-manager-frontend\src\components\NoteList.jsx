import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Calendar, FileText, Hash, Search, Tag, X } from 'lucide-react'
import { useEffect, useState } from 'react'
import '../App.css'

const NoteList = ({ onNoteSelect, selectedNoteId }) => {
  const [notes, setNotes] = useState([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [filteredNotes, setFilteredNotes] = useState([])

  useEffect(() => {
    fetchNotes()
  }, [])

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredNotes(notes)
    } else {
      const filtered = notes.filter(note =>
        note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.summary?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.tags.some(tag => tag.name.toLowerCase().includes(searchQuery.toLowerCase()))
      )
      setFilteredNotes(filtered)
    }
  }, [searchQuery, notes])

  const fetchNotes = async () => {
    try {
      const response = await fetch('/api/notes')
      const data = await response.json()
      setNotes(data.notes || [])
      setLoading(false)
    } catch (error) {
      console.error('Error fetching notes:', error)
      setLoading(false)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const truncateText = (text, maxLength = 150) => {
    if (!text) return ''
    return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  return (
    <div className="space-y-6 h-full flex flex-col">
      {/* Enhanced Search Bar */}
      <div className="relative">
        <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-muted-foreground">
          <Search className="h-5 w-5" />
        </div>
        <Input
          placeholder="Search notes, content, tags..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className="pl-12 pr-4 py-3 text-base bg-muted/30 border-border/50 rounded-xl focus:bg-background focus:ring-2 focus:ring-primary/20 transition-all duration-200"
        />
        {searchQuery && (
          <button
            onClick={() => setSearchQuery('')}
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>

      {/* Enhanced Notes Count */}
      <div className="flex items-center justify-between">
        <div className="text-sm font-medium text-muted-foreground">
          <span className="text-foreground font-semibold">{filteredNotes.length}</span> {filteredNotes.length === 1 ? 'note' : 'notes'}
          {searchQuery && (
            <span className="ml-2 px-2 py-1 bg-primary/10 text-primary rounded-md text-xs">
              matching "{searchQuery}"
            </span>
          )}
        </div>
        {filteredNotes.length > 0 && (
          <div className="text-xs text-muted-foreground">
            Updated {formatDate(filteredNotes[0]?.updated_at)}
          </div>
        )}
      </div>

      {/* Enhanced Notes List */}
      <div className="flex-1 overflow-y-auto scrollbar-thin space-y-4">
        {filteredNotes.map((note) => (
          <Card
            key={note.id}
            className={`group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 border-0 ${
              selectedNoteId === note.id
                ? 'ring-2 ring-primary shadow-lg bg-primary/5 scale-[1.02]'
                : 'hover:shadow-xl bg-card/80 backdrop-blur-sm'
            }`}
            onClick={() => onNoteSelect(note)}
          >
            <CardHeader className="pb-4">
              <div className="flex items-start justify-between">
                <div className="flex-1 min-w-0">
                  <CardTitle className="text-lg font-bold line-clamp-2 group-hover:text-primary transition-colors duration-200">
                    {note.title}
                  </CardTitle>
                  <CardDescription className="flex items-center gap-3 mt-2 text-xs">
                    <div className="flex items-center gap-1">
                      <FileText className="h-3 w-3 text-primary/70" />
                      <span className="uppercase font-medium text-primary/70">{note.file_type}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Calendar className="h-3 w-3" />
                      <span>{formatDate(note.updated_at)}</span>
                    </div>
                  </CardDescription>
                </div>
                {selectedNoteId === note.id && (
                  <div className="w-3 h-3 bg-primary rounded-full animate-pulse"></div>
                )}
              </div>
            </CardHeader>

            <CardContent className="pt-0 pb-6">
              {/* Enhanced Summary */}
              {note.summary && (
                <div className="mb-4">
                  <p className="text-sm text-muted-foreground line-clamp-3 leading-relaxed">
                    {truncateText(note.summary, 200)}
                  </p>
                </div>
              )}

              {/* Enhanced Tags */}
              {note.tags && note.tags.length > 0 && (
                <div className="flex flex-wrap gap-2 mb-4">
                  {note.tags.slice(0, 3).map((tag) => (
                    <Badge
                      key={tag.id}
                      variant="secondary"
                      className="text-xs px-2 py-1 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors"
                    >
                      <Tag className="h-2 w-2 mr-1" />
                      {tag.name}
                    </Badge>
                  ))}
                  {note.tags.length > 3 && (
                    <Badge variant="outline" className="text-xs px-2 py-1 border-muted-foreground/30">
                      +{note.tags.length - 3} more
                    </Badge>
                  )}
                </div>
              )}

              {/* Enhanced Topics */}
              {note.topics && note.topics.length > 0 && (
                <div className="flex flex-wrap gap-2">
                  {note.topics.slice(0, 2).map((topic, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs px-2 py-1 bg-accent/20 text-accent-foreground border-accent/30 hover:bg-accent/30 transition-colors"
                    >
                      <Hash className="h-2 w-2 mr-1" />
                      {topic}
                    </Badge>
                  ))}
                  {note.topics.length > 2 && (
                    <Badge variant="outline" className="text-xs px-2 py-1 border-muted-foreground/30">
                      +{note.topics.length - 2} topics
                    </Badge>
                  )}
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

        {filteredNotes.length === 0 && !loading && (
          <div className="text-center py-16">
            <div className="relative mb-6">
              <div className="absolute inset-0 bg-primary/10 rounded-full blur-xl"></div>
              <FileText className="relative h-16 w-16 text-primary/60 mx-auto" />
            </div>
            <h3 className="text-xl font-bold mb-3 text-foreground">
              {searchQuery ? 'No matching notes' : 'No notes yet'}
            </h3>
            <p className="text-muted-foreground text-base leading-relaxed max-w-sm mx-auto">
              {searchQuery
                ? 'Try adjusting your search terms or browse all notes'
                : 'Import your first notes to start building your knowledge base'}
            </p>
            {searchQuery && (
              <button
                onClick={() => setSearchQuery('')}
                className="mt-4 px-4 py-2 bg-primary/10 text-primary rounded-lg hover:bg-primary/20 transition-colors text-sm font-medium"
              >
                Clear search
              </button>
            )}
          </div>
        )}
    </div>
  )
}

export default NoteList

