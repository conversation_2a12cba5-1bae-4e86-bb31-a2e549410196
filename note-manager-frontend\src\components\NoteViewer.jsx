import { Badge } from '@/components/ui/badge'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import {
    Brain,
    Calendar,
    Clock,
    Edit3,
    FileText,
    Hash,
    Save,
    Sparkles,
    Tag,
    X
} from 'lucide-react'
import { useEffect, useState } from 'react'
import '../App.css'

const NoteViewer = ({ note, onNoteUpdate }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [editContent, setEditContent] = useState('')
  const [saving, setSaving] = useState(false)
  const [processing, setProcessing] = useState(false)

  useEffect(() => {
    if (note) {
      setEditContent(note.content)
    }
  }, [note])

  if (!note) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center max-w-md">
          <div className="relative mb-8">
            <div className="absolute inset-0 bg-gradient-primary rounded-full blur-2xl opacity-20"></div>
            <FileText className="relative h-20 w-20 text-primary mx-auto" />
          </div>
          <h3 className="text-2xl font-bold mb-4 text-gradient">Select a note to begin</h3>
          <p className="text-muted-foreground text-lg leading-relaxed">
            Choose a note from the sidebar to view and edit its content, or explore your knowledge graph
          </p>
          <div className="mt-8 p-4 bg-muted/30 rounded-xl border border-border/50">
            <p className="text-sm text-muted-foreground">
              💡 <strong>Tip:</strong> Use the search bar to quickly find specific notes or topics
            </p>
          </div>
        </div>
      </div>
    )
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      const response = await fetch(`/api/notes/${note.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          content: editContent,
          process_with_llm: true
        })
      })

      if (response.ok) {
        const updatedNote = await response.json()
        onNoteUpdate(updatedNote)
        setIsEditing(false)
      } else {
        console.error('Failed to save note')
      }
    } catch (error) {
      console.error('Error saving note:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleProcessWithLLM = async () => {
    setProcessing(true)
    try {
      const response = await fetch(`/api/llm/process/${note.id}`, {
        method: 'POST'
      })

      if (response.ok) {
        const result = await response.json()
        onNoteUpdate(result.note)
      } else {
        console.error('Failed to process note with LLM')
      }
    } catch (error) {
      console.error('Error processing note:', error)
    } finally {
      setProcessing(false)
    }
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const renderContent = (content) => {
    // Simple markdown-like rendering for display
    return content
      .split('\n')
      .map((line, index) => {
        if (line.startsWith('# ')) {
          return <h1 key={index} className="text-2xl font-bold mb-4 mt-6">{line.substring(2)}</h1>
        } else if (line.startsWith('## ')) {
          return <h2 key={index} className="text-xl font-semibold mb-3 mt-5">{line.substring(3)}</h2>
        } else if (line.startsWith('### ')) {
          return <h3 key={index} className="text-lg font-medium mb-2 mt-4">{line.substring(4)}</h3>
        } else if (line.startsWith('- ') || line.startsWith('* ')) {
          return <li key={index} className="ml-4 mb-1">{line.substring(2)}</li>
        } else if (line.trim() === '') {
          return <br key={index} />
        } else {
          return <p key={index} className="mb-2 leading-relaxed">{line}</p>
        }
      })
  }

  return (
    <div className="h-full flex flex-col">
      {/* Enhanced Header */}
      <div className="mb-8">
        <div className="flex items-start justify-between mb-6">
          <div className="flex-1 min-w-0">
            <h1 className="text-3xl font-bold mb-3 text-gradient leading-tight">
              {note.title}
            </h1>
            <div className="flex flex-wrap items-center gap-4 text-sm">
              <div className="flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-lg">
                <FileText className="h-4 w-4" />
                <span className="uppercase font-semibold">{note.file_type}</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Calendar className="h-4 w-4" />
                <span>Updated {formatDate(note.updated_at)}</span>
              </div>
              <div className="flex items-center gap-2 text-muted-foreground">
                <Clock className="h-4 w-4" />
                <span>Created {formatDate(note.created_at)}</span>
              </div>
            </div>
          </div>
          <div className="flex gap-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handleProcessWithLLM}
              disabled={processing}
              className="bg-gradient-accent hover:opacity-90 border-0 text-white shadow-lg transition-all duration-200"
            >
              {processing ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Sparkles className="h-4 w-4" />
              )}
              <span className="ml-2 font-medium">
                {processing ? 'Processing...' : 'AI Enhance'}
              </span>
            </Button>
            {!isEditing ? (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setIsEditing(true)}
                className="bg-white/50 hover:bg-white/70 border-border/50 backdrop-blur-sm transition-all duration-200"
              >
                <Edit3 className="h-4 w-4 mr-2" />
                <span className="font-medium">Edit</span>
              </Button>
            ) : (
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setIsEditing(false)
                    setEditContent(note.content)
                  }}
                  className="bg-white/50 hover:bg-white/70 border-border/50 backdrop-blur-sm transition-all duration-200"
                >
                  <X className="h-4 w-4 mr-2" />
                  <span className="font-medium">Cancel</span>
                </Button>
                <Button
                  size="sm"
                  onClick={handleSave}
                  disabled={saving}
                  className="btn-gradient shadow-lg"
                >
                  {saving ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Save className="h-4 w-4" />
                  )}
                  <span className="ml-2 font-medium">
                    {saving ? 'Saving...' : 'Save'}
                  </span>
                </Button>
              </div>
            )}
          </div>
        </div>

        {/* Enhanced Metadata */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* Summary */}
          {note.summary && (
            <Card className="card-enhanced border-0 shadow-lg">
              <CardHeader className="bg-gradient-secondary rounded-t-xl pb-3">
                <CardTitle className="text-sm font-semibold flex items-center gap-2 text-primary">
                  <Brain className="h-4 w-4" />
                  AI Summary
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <p className="text-sm text-muted-foreground leading-relaxed">
                  {note.summary}
                </p>
              </CardContent>
            </Card>
          )}

          {/* Tags */}
          {note.tags && note.tags.length > 0 && (
            <Card className="card-enhanced border-0 shadow-lg">
              <CardHeader className="bg-gradient-secondary rounded-t-xl pb-3">
                <CardTitle className="text-sm font-semibold flex items-center gap-2 text-primary">
                  <Tag className="h-4 w-4" />
                  Tags ({note.tags.length})
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="flex flex-wrap gap-2">
                  {note.tags.map((tag) => (
                    <Badge
                      key={tag.id}
                      variant="secondary"
                      className="text-xs px-3 py-1 bg-primary/10 text-primary border-primary/20 hover:bg-primary/20 transition-colors"
                    >
                      {tag.name}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

        {/* Topics */}
        {note.topics && note.topics.length > 0 && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium flex items-center gap-2">
                <Hash className="h-4 w-4" />
                Topics ({note.topics.length})
              </CardTitle>
            </CardHeader>
            <CardContent className="pt-0">
              <div className="flex flex-wrap gap-2">
                {note.topics.map((topic, index) => (
                  <Badge
                    key={index}
                    variant="outline"
                    className="text-xs bg-blue-50 text-blue-700 border-blue-200"
                  >
                    {topic}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Content */}
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <CardTitle className="text-lg">Content</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          {isEditing ? (
            <Textarea
              value={editContent}
              onChange={(e) => setEditContent(e.target.value)}
              className="flex-1 min-h-[400px] font-mono text-sm resize-none"
              placeholder="Enter note content..."
            />
          ) : (
            <div className="flex-1 overflow-auto">
              <div className="prose prose-sm max-w-none">
                {renderContent(note.content)}
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}

export default NoteViewer

